import { ClickAwayListenerProps } from '@mui/material'
import { format } from 'date-fns'
import {
  AllowedZonedMode,
  IAllowedZone,
  ICalculationValue,
  ISaveDataVaultInput,
  TStatusUpdateModalVault,
} from 'entities/api/calculationsManager.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import {
  CalculationColumn,
  CalculationTaskType,
  PlanningStage,
  PlanningStageRussia,
  PlantParameter,
  RegistryType,
  TaskStatus,
} from 'entities/shared/common.entities.ts'
import { ICalculationsPageStore } from 'entities/store/calculationPageStore.entities.ts'
import { ITempGesCell, MessagesWarnings } from 'entities/widgets/Vault.entities.ts'
import Handsontable from 'handsontable'
import { HyperFormula } from 'hyperformula'
import { makeAutoObservable, runInAction, toJS } from 'mobx'
import {
  getValueEminEmax,
  validateDailyOutputMax,
  validateDailyOutputMin,
  validateDailyOutputPlan,
} from 'pages/CalculationsPage/lib'
import { Plant } from 'pages/CalculationsPage/ui/StationBody/entities'
import {
  additionalValidation,
  getColorStage,
  getGesCellPropByTableCoords,
  getStatusCell,
  getStyledComment,
  getValidGES,
  hours,
  isRgu,
  keysGES,
  typeKeysGes,
} from 'pages/CalculationsPage/ui/StationBody/lib'
import { makePMax, makePMaxFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMaxFormula'
import { destroyPMaxFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMaxFormula/makePMaxFormula.ts'
import { makePMin, makePMinFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMinFormula'
import { destroyPMinFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMinFormula/makePMinFormula.ts'
import api from 'shared/api'
import { calcCellFromAlphabet } from 'shared/lib/alphabet'
import { debounce } from 'shared/lib/debounce'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { klona } from 'shared/lib/klona'
import { prepareUniqueArr } from 'shared/lib/prepareData'
import { round } from 'shared/lib/round'
import { IVaultUpdateData } from 'stores/CalculationsPageStore/CalculationsPageStore.ts'
import { generateHeaderKeyIndexMap } from 'stores/CalculationsPageStore/lib/generateHeaderKeyIndexMap'
import {
  IAvrchmSpreadsheetColumn,
  InputValueKeys,
  IVaultStore,
} from 'stores/CalculationsPageStore/VaultStore/vaultStore.types.ts'
import type { RootStore } from 'stores/RootStore.ts'
import { calculateSpreadsheetValueByFormula, getSpreadsheetSelectedCells } from 'widgets/Spreadsheet/ui/lib'
import {
  getMapOfNestedHeadersBasedOnConfig,
  MapOfNestedHeadersBasedOnConfig,
} from 'widgets/Spreadsheet/ui/lib/getMapOfNestedHeadersBasedOnConfig/getMapOfNestedHeadersBasedOnConfig.ts'
import { IInputResultItemProp } from 'widgets/Spreadsheet/ui/Spreadsheet.tsx'

import { vaultHeaderClassNameConfig } from '../lib'

const initVaultSpreadsheet: IVaultStore['vaultSpreadsheet'] = {
  colNumberPerStation: [],
  nestedHeaders: [],
  customHeaders: [],
  column: [],
  collapsibleColumns: [],
  data: [],
  cell: [],
  inputResultProps: [],
}

export class VaultStore {
  rootStore: ICalculationsPageStore['rootStore']
  vaultLoadDataStatus: IVaultStore['vaultLoadDataStatus'] = 'IN_PROCESS'
  vaultSpreadsheet: IVaultStore['vaultSpreadsheet'] = initVaultSpreadsheet
  originalVaultSpreadsheet: IVaultStore['vaultSpreadsheet'] = initVaultSpreadsheet
  plantsData: IVaultStore['plantsData'] = []
  plantsDataOriginal: IVaultStore['plantsDataOriginal'] = []
  vaultData: IVaultStore['vaultData'] = []
  vaultDataOriginal: IVaultStore['vaultDataOriginal'] = []
  statusVaultUpdateData: IVaultStore['statusVaultUpdateData'] = 'NOT_EXECUTED'
  actionsLeft: IVaultStore['actionsLeft'] = []
  floodsLeft: IVaultStore['floodsLeft'] = []
  idsEditFloodsLeft: IVaultStore['idsEditFloodsLeft'] = []
  isLoadingAvrchmTes: IVaultStore['isLoadingAvrchmTes'] = false
  isLoadingVaultUpdateData: IVaultStore['isLoadingVaultUpdateData'] = true
  isModalUpdateVault: IVaultStore['isModalUpdateVault'] = false
  vaultUpdateData: IVaultStore['vaultUpdateData'] = []
  isViewModalAcceptedVault: IVaultStore['isViewModalAcceptedVault'] = false
  plantsAcceptVault: IVaultStore['plantsAcceptVault'] = []
  plantsAcceptVaultSelected: IVaultStore['plantsAcceptVaultSelected'] = []
  inputValues: IVaultStore['inputValues'] = []
  inputValuesChanged: IVaultStore['inputValuesChanged'] = false
  editCellsUp: IVaultStore['editCellsUp'] = []
  selectedCellsBeforeFix: IVaultStore['selectedCellsBeforeFix'] = []
  shouldUpdateVault: IVaultStore['shouldUpdateVault'] = false
  handsontableInstance: Handsontable | null = null

  private _originalInputValues: IVaultStore['_originalInputValues'] = []
  private _floodsLeftOriginal: IVaultStore['floodsLeftOriginal'] = []
  private _selectedCellIdx: IVaultStore['_selectedCellIdx'] = null

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    this.debouncedInitLoadDataVault = debounce(() => this.initLoadDataVault(true), 300)
    makeAutoObservable(this, {
      // Отключаем наблюдение, что бы ускорить инициализацию таблицы
      originalVaultSpreadsheet: false,
    })
  }

  get selectedCellAllowedZones() {
    if (this._selectedCellIdx === null) return []

    return this.vaultSpreadsheet.cell[this._selectedCellIdx].allowedZones
  }

  get isChangeFloodMode() {
    return this.idsEditFloodsLeft.length > 0
  }

  get isEditRows() {
    return (
      this.editCellsUp.length > 0 ||
      this.isChangeFloodMode ||
      this._avrchmStore.isChangedAvrchmSpreadsheet ||
      this.inputValuesChanged
    )
  }

  get disabledAccept() {
    return this.plantsAcceptVaultSelected.length === 0
  }

  get inputResultCellIndexes(): number[] {
    let currentStep = 0
    const res: number[] = []
    this.vaultSpreadsheet.colNumberPerStation?.forEach((stationColNum) => {
      res.push(currentStep, currentStep + 1, currentStep + 2)
      currentStep += stationColNum
    })

    return res
  }

  get inputResultStartColumnIndexes(): number[] {
    let currentStep = 0
    const res: number[] = []
    this.vaultSpreadsheet.colNumberPerStation?.forEach((stationColNum) => {
      currentStep += stationColNum
      res.push(currentStep)
    })

    return res
  }

  get avrchmColumns() {
    const tmpAvrchmColumns: IAvrchmSpreadsheetColumn[] = []
    this.vaultSpreadsheet.cell.forEach((cell) => {
      if (cell.keyStation === 'AVRCHM_LOAD') {
        if (tmpAvrchmColumns.findIndex((item) => Number(item.plantId) === Number(cell.idStation)) === -1) {
          tmpAvrchmColumns.push({ columnIdx: cell.col, plantId: Number(cell.idStation) })
        }
      }
    })

    return tmpAvrchmColumns
  }

  get _columnsWithExtraDataForHeaderValidation(): Handsontable.ColumnSettings[] {
    const columnsError = this.vaultSpreadsheet.cell.reduce(
      (acc, cell) => {
        if (!acc[cell.col]) {
          acc[cell.col] = cell.renderer.includes('isNotValid')
        }

        return acc
      },
      Array.from({ length: this.vaultSpreadsheet.column.length }),
    )
    let currentPlantStartIdx = 0
    this.vaultSpreadsheet.inputResultProps.forEach((inputResultProp, idx) => {
      columnsError[currentPlantStartIdx] = columnsError[currentPlantStartIdx] || !inputResultProp.wMin.isValid
      columnsError[currentPlantStartIdx + 1] = columnsError[currentPlantStartIdx + 1] || !inputResultProp.pGen.isValid
      columnsError[currentPlantStartIdx + 2] = columnsError[currentPlantStartIdx + 2] || !inputResultProp.wMax.isValid
      currentPlantStartIdx += this.vaultSpreadsheet.nestedHeaders[0][idx].colspan
    })
    if (this.vaultSpreadsheet.column.length === 0) {
      return []
    }

    return this.vaultSpreadsheet.column.map((column, idx) => ({
      ...column,
      plantOptimized: this.vaultSpreadsheet.cell[idx]['type'].includes('plantOptimized'),
      hasError: columnsError[idx],
    }))
  }

  get nestedHeaderClassNameMap(): MapOfNestedHeadersBasedOnConfig {
    if (
      this.vaultSpreadsheet.nestedHeaders.length === 0 ||
      this._columnsWithExtraDataForHeaderValidation.length === 0
    ) {
      return {}
    }

    return getMapOfNestedHeadersBasedOnConfig(
      this.vaultSpreadsheet.nestedHeaders,
      this._columnsWithExtraDataForHeaderValidation,
      vaultHeaderClassNameConfig,
    )
  }

  get firstColumnIndexSplittedByStation() {
    if (this.vaultSpreadsheet.nestedHeaders.length === 0) return []

    return this.vaultSpreadsheet.nestedHeaders[0].reduce((acc, el, idx, initialArr) => {
      if (acc.length === 0) {
        return [0]
      } else if (acc.length === 1) {
        acc.push(initialArr[idx - 1].colspan)
      } else {
        acc.push(acc[idx - 1] + initialArr[idx - 1].colspan)
      }

      return acc
    }, [] as number[])
  }

  private get _avrchmStore() {
    return this.rootStore.calculationsPageStore.avrchmStore
  }

  private get _date() {
    return this.rootStore.calculationsPageStore.date
  }

  private get _selectLeftMenu() {
    return this.rootStore.calculationsPageStore.selectLeftMenu
  }

  private get _plantsListForAside() {
    return this.rootStore.calculationsPageStore.plantsListForAside
  }

  private get _isFinishStage() {
    if (this.rootStore.godModeStore.godMode) return false

    return this.rootStore.calculationsPageStore.isFinishStage
  }

  private get _planningStage(): PlanningStage {
    const actualStage = this.rootStore.calculationsPageStore.actualStage
    const selectedStage = this.rootStore.calculationsPageStore.selectedStage
    if (selectedStage === 'ACTUAL' && actualStage !== null) {
      return actualStage.code
    }

    return selectedStage as PlanningStage
  }

  private get _isLastDay() {
    if (this.rootStore.godModeStore.godMode) return false

    return this.rootStore.calculationsPageStore.isLastDay
  }

  private get _editMode() {
    return this.rootStore.calculationsPageStore.editMode
  }

  private get _displayedPlants() {
    return this._plantsListForAside.filter((el) => el.mixing)
  }

  private readonly _getPlantParamsByAvrchmColIdx = (rguAVRCHMs: { [key: number]: number[] }, col: number | string) => {
    let indexStationAVRCHM = -1
    let isRguAvrchm = false
    Object.entries(rguAVRCHMs).forEach(([stationIdx, rguIdxs], idx) => {
      if (Number(stationIdx) === Number(col) || rguIdxs.includes(Number(col))) {
        isRguAvrchm = isRguAvrchm || rguIdxs.includes(Number(col))
        indexStationAVRCHM = idx
      }
    })

    return {
      indexStationAVRCHM,
      isRguAvrchm,
    }
  }

  private readonly _updateInputResultProps = () => {
    let rowResultData = []
    // При первом рендере таблица не проинициализирована, в этом случае добавляем строку сумму и
    // создаем искусственную таблицу
    const data = klona(this.vaultSpreadsheet.data)
    if (data.length > 0) {
      data.push(
        data[0].map((_, index) => {
          let res: string[] = []
          data.forEach((_, indexRow) => {
            res = [...res, `${calcCellFromAlphabet(index + 1)}${indexRow + 1}`]
          })

          return `=ROUND((${res.join('+')})/1000,3)`
        }),
      )
    }
    const htInstance = new Handsontable(document.createElement('div'), {
      data,
      licenseKey: 'non-commercial-and-evaluation',
      formulas: {
        engine: HyperFormula.buildEmpty({
          licenseKey: 'internal-use-in-handsontable',
        }),
      },
    })
    rowResultData = htInstance.getDataAtRow(24) ?? []
    this.vaultSpreadsheet.inputResultProps = this.vaultSpreadsheet.colNumberPerStation.map((stationColNum, idx) => {
      const sumIdx = [...this.inputResultCellIndexes].splice(idx * 3, idx * 3 + 3)
      const plantFullData = this.plantsData.find((el) => el.plantId === this._displayedPlants[idx]?.plantId)
      const viewOnly = this._displayedPlants[idx]?.viewOnly ?? false
      const FLOOD_MODE_WATCH = this.floodsLeft?.some((id) => id === this._displayedPlants[idx]?.plantId) ?? false
      const valueMin = FLOOD_MODE_WATCH ? `=${calcCellFromAlphabet(sumIdx[0] + 1)}25` : this.inputValues[idx]?.W_MIN
      const valueMax = FLOOD_MODE_WATCH ? `=${calcCellFromAlphabet(sumIdx[2] + 1)}25` : this.inputValues[idx]?.W_MAX
      const valueGen = FLOOD_MODE_WATCH
        ? `=${calcCellFromAlphabet(sumIdx[2] + 1)}25`
        : this.inputValues[idx]?.P_GEN_TARGET
      const isWMin = this.inputValues[idx] ? Object.keys(this.inputValues[idx])?.some((el) => el === 'W_MIN') : false
      const isWMax = this.inputValues[idx] ? Object.keys(this.inputValues[idx])?.some((el) => el === 'W_MAX') : false
      const isPGen = this.inputValues[idx]
        ? Object.keys(this.inputValues[idx])?.some((el) => el === 'P_GEN_TARGET')
        : false

      const inputValuesRes: Omit<IVaultStore['inputValues'][0], 'plantId'> = {
        W_MIN: isWMin ? valueMin : undefined,
        W_MAX: isWMax ? valueMax : undefined,
        P_GEN_TARGET: isPGen ? valueGen : undefined,
      }

      if (FLOOD_MODE_WATCH) {
        if (plantFullData?.plantOptimized) {
          inputValuesRes['W_MIN'] = rowResultData[sumIdx[0]]
          inputValuesRes['W_MAX'] = rowResultData[sumIdx[2]]
        } else {
          inputValuesRes['P_GEN_TARGET'] = rowResultData[sumIdx[2]]
        }
      }

      let validate_P_GEN
      let validate_W_MIN
      let validate_W_MAX

      if (htInstance) {
        validate_P_GEN = validateDailyOutputPlan(
          htInstance,
          inputValuesRes,
          {
            pMin: sumIdx[0],
            pGen: sumIdx[1],
            pMax: sumIdx[2],
          },
          isPGen,
          plantFullData?.rgus.length ?? 0,
        )

        validate_W_MIN = validateDailyOutputMin(
          htInstance,
          inputValuesRes,
          {
            pMin: sumIdx[0],
            pGen: sumIdx[1],
            pMax: sumIdx[2],
          },
          isWMin,
          plantFullData?.rgus.length ?? 0,
        )

        validate_W_MAX = validateDailyOutputMax(
          htInstance,
          inputValuesRes,
          {
            pMin: sumIdx[0],
            pGen: sumIdx[1],
            pMax: sumIdx[2],
          },
          isWMax,
          plantFullData?.rgus.length ?? 0,
        )
      }

      const isErrorPGENTARGET = typeof validate_P_GEN !== 'string'
      const isErrorWMIN = typeof validate_W_MIN !== 'string'
      const isErrorWMAX = typeof validate_W_MAX !== 'string'

      return {
        stationColNum: stationColNum || 0,
        wMin: {
          active: isWMin,
          disabled:
            this._displayedPlants[idx]?.accepted ||
            viewOnly ||
            this._isLastDay ||
            FLOOD_MODE_WATCH ||
            plantFullData?.parameters?.E_MAX_E_MIN?.value.turnedOn ||
            !this._editMode ||
            this._isFinishStage,
          value: valueMin,
          isValid: isErrorWMIN,
          comment: validate_W_MIN !== undefined ? getStyledComment(validate_W_MIN) : undefined,
        },
        pGen: {
          active: isPGen,
          disabled:
            this._displayedPlants[idx]?.accepted ||
            viewOnly ||
            this._isLastDay ||
            FLOOD_MODE_WATCH ||
            !this._editMode ||
            this._isFinishStage,
          value: valueGen,
          isValid: isErrorPGENTARGET,
          comment: validate_P_GEN !== undefined ? getStyledComment(validate_P_GEN) : undefined,
        },
        wMax: {
          active: isWMax,
          disabled:
            this._displayedPlants[idx]?.accepted ||
            viewOnly ||
            this._isLastDay ||
            FLOOD_MODE_WATCH ||
            !this._editMode ||
            this._isFinishStage,
          value: valueMax,
          isValid: isErrorWMAX,
          comment: validate_W_MAX !== undefined ? getStyledComment(validate_W_MAX) : undefined,
        },
      }
    })
    htInstance.destroy()
  }

  resetStore = () => {
    this.vaultLoadDataStatus = 'IN_PROCESS'
    this.vaultSpreadsheet = initVaultSpreadsheet
    this._selectedCellIdx = null
    this.plantsData = []
    this.plantsDataOriginal = []
    this.plantsAcceptVault = []
    this.plantsAcceptVaultSelected = []
    this.vaultData = []
    this.vaultDataOriginal = []
    this.statusVaultUpdateData = 'NOT_EXECUTED'
    this.isLoadingVaultUpdateData = true
    this.isModalUpdateVault = false
    this.actionsLeft = []
    this.floodsLeft = []
    this.idsEditFloodsLeft = []
    this._floodsLeftOriginal = []
    this.vaultUpdateData = []
    this.isViewModalAcceptedVault = false
    this.plantsAcceptVaultSelected = []
    this.inputValues = []
    this.shouldUpdateVault = false
    this._originalInputValues = []
    this.editCellsUp = []
    this.inputValuesChanged = false
  }

  setEditCellsUp = (editCellsUp: string[]) => {
    this.editCellsUp = Array.from(new Set(editCellsUp))
  }

  setVaultLoadDataStatus: IVaultStore['setVaultLoadDataStatus'] = (status) => {
    this.vaultLoadDataStatus = status
  }

  setSpreadsheetCellProp: IVaultStore['setSpreadsheetCellProp'] = (cell) => {
    this.vaultSpreadsheet.cell = cell
  }

  setSelectedCell: IVaultStore['setSelectedCell'] = (selectedRow, selectedCol) => {
    if (selectedRow < 0 || selectedCol < 0) return
    if (selectedRow > 23) {
      this._selectedCellIdx = null

      return
    }
    this._selectedCellIdx = selectedRow * this.vaultSpreadsheet.column.length + selectedCol
  }

  setDeselectedCell: IVaultStore['setDeselectedCell'] = () => {
    this._selectedCellIdx = null
  }

  setSelectedCellsBeforeFix = (selectedCellsBeforeFix: IVaultStore['selectedCellsBeforeFix']) => {
    this.selectedCellsBeforeFix = selectedCellsBeforeFix
  }

  setShouldUpdateVault: IVaultStore['setShouldUpdateVault'] = (shouldUpdateVault) => {
    this.shouldUpdateVault = shouldUpdateVault
  }

  setHandsontableInstance = (handsontableInstance: Handsontable | null) => {
    this.handsontableInstance = handsontableInstance
  }

  convertVaultDataToSpreadsheetProps: IVaultStore['convertVaultDataToSpreadsheetProps'] = ({
    vaultData,
    plantsData,
  }) => {
    const calculationsPageStore = this.rootStore.calculationsPageStore
    const isLastDay = calculationsPageStore.isLastDay
    const editMode = calculationsPageStore.editMode
    const viewOnly = calculationsPageStore.viewOnly || !calculationsPageStore.isActualStage
    const plantsListForAside = calculationsPageStore.plantsListForAside

    let colNumberPerStation: IVaultStore['vaultSpreadsheet']['colNumberPerStation'] = []
    let nestedHeaders: IVaultStore['vaultSpreadsheet']['nestedHeaders'] = []
    let customHeaders: IVaultStore['vaultSpreadsheet']['customHeaders'] = []
    let column: IVaultStore['vaultSpreadsheet']['column'] = []
    let collapsibleColumns: IVaultStore['vaultSpreadsheet']['collapsibleColumns'] = []
    let data: IVaultStore['vaultSpreadsheet']['data'] = []
    let cell: IVaultStore['vaultSpreadsheet']['cell'] = []
    if (vaultData) {
      let nestedHeadersFirst: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
      let nestedHeadersSecond: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
      let nestedHeadersThird: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
      let nestedHeadersFourth: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
      let nestedHeadersFifth: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
      let collapsibleColumnsRes: IVaultStore['vaultSpreadsheet']['collapsibleColumns'] = []

      let dataTemp: IVaultStore['vaultSpreadsheet']['data'] = []
      const stations = plantsListForAside.filter((el) => el.mixing)
      hours.forEach((_, index) => {
        const dataForHour = vaultData[index]
        let dataTempHour: IVaultStore['vaultSpreadsheet']['data'][0] = []
        let cursor: number = 0
        stations.forEach((station) => {
          let dataPerStation: IVaultStore['vaultSpreadsheet']['data'][0] = []
          const plant = plantsData?.find((ps) => ps.plantId === station.plantId)
          const plantRgus = plant?.rgus ?? []
          const plantZones: IAllowedZone[] = (vaultData[index][`${plant?.plantId}-allowedZones`] ??
            []) as unknown as IAllowedZone[]
          const FLOOD_MODE_WATCH = plant?.inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false
          const REGULATED_UNIT = plant?.regulatedUnit ?? 'PLANT'
          const limit_min_cell = calcCellFromAlphabet(
            cursor + (plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + 1,
          )
          const limit_max_cell = calcCellFromAlphabet(
            cursor + (plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + 1 + 1,
          )
          const avrchm_cell = calcCellFromAlphabet(cursor + (plantRgus.length + 1) * 3 + 1 + 1)
          const rm_min = calcCellFromAlphabet(
            cursor + (plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + (plantRgus.length + 1) * 2 + 1,
          )
          const rm_max = calcCellFromAlphabet(
            cursor + (plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + (plantRgus.length + 1) * 2 + 1 + 1,
          )
          const modes_min = calcCellFromAlphabet(
            cursor +
              (plantRgus.length + 1) * 3 +
              (plantRgus.length + 1) * 2 +
              (plantRgus.length + 1) * 2 +
              (plantRgus.length + 1) * 2 +
              1,
          )
          const modes_max = calcCellFromAlphabet(
            cursor +
              (plantRgus.length + 1) * 3 +
              (plantRgus.length + 1) * 2 +
              (plantRgus.length + 1) * 2 +
              (plantRgus.length + 1) * 2 +
              1 +
              1,
          )
          const P_GEN_RESULT = `${dataForHour[`${station?.plantId}-P_GEN`]}`
          const RESERV_MAX =
            dataForHour[`${station?.plantId}-RESERVES_MAX`] !== undefined
              ? `${dataForHour[`${station?.plantId}-RESERVES_MAX`]}`
              : ''
          const AVRCHM_LOAD = FLOOD_MODE_WATCH //|| IS_ACTIVE_FLOOD_MODE_WATCH
            ? 0
            : `${dataForHour[`${station?.plantId}-AVRCHM_LOAD`]}`
          const NPRCH =
            dataForHour[`${station?.plantId}-NPRCH`] !== undefined ? `${dataForHour[`${station?.plantId}-NPRCH`]}` : ''
          const P_MIN = `=ROUND(MAX(${rm_min}${index + 1},${modes_min}${index + 1}),3)`
          const P_MAX = `=ROUND(MIN(${rm_max}${index + 1},${modes_max}${index + 1}),3)`
          const CM_P_MIN =
            dataForHour[`${station?.plantId}-CM_P_MIN`] !== undefined
              ? `${dataForHour[`${station?.plantId}-CM_P_MIN`]}`
              : ''
          const CM_P_MAX =
            dataForHour[`${station?.plantId}-CM_P_MAX`] !== undefined
              ? `${dataForHour[`${station?.plantId}-CM_P_MAX`]}`
              : ''
          const MODES_P_MIN =
            dataForHour[`${station?.plantId}-MODES_P_MIN`] !== undefined
              ? `${dataForHour[`${station?.plantId}-MODES_P_MIN`]}`
              : ''
          const MODES_P_MAX =
            dataForHour[`${station?.plantId}-MODES_P_MAX`] !== undefined
              ? `${dataForHour[`${station?.plantId}-MODES_P_MAX`]}`
              : ''
          const MODES_DECLARED =
            dataForHour[`${station?.plantId}-MODES_DECLARED`] !== undefined
              ? `${dataForHour[`${station?.plantId}-MODES_DECLARED`]}`
              : ''
          let firstRgu: IVaultStore['vaultSpreadsheet']['data'][0] = []
          let secondRgu: IVaultStore['vaultSpreadsheet']['data'][0] = []
          let thirdRgu: IVaultStore['vaultSpreadsheet']['data'][0] = []
          let fourthRgu: IVaultStore['vaultSpreadsheet']['data'][0] = []
          let fifthRgu: IVaultStore['vaultSpreadsheet']['data'][0] = []
          let rgusForPmin: IVaultStore['vaultSpreadsheet']['data'][0] = []
          let rgusForPmax: IVaultStore['vaultSpreadsheet']['data'][0] = []
          plantRgus.forEach((el, indexRgu) => {
            rgusForPmin = [...rgusForPmin, `${calcCellFromAlphabet(cursor + (indexRgu + 1) * 3 + 1)}${index + 1}`]
            rgusForPmax = [...rgusForPmax, `${calcCellFromAlphabet(cursor + (indexRgu + 1) * 3 + 3)}${index + 1}`]
            const dataForHour = el.rows[index].cells
            const CM_P_MIN = dataForHour.find((cell) => cell.column === 'CM_P_MIN')?.value
            const CM_P_MAX = dataForHour.find((cell) => cell.column === 'CM_P_MAX')?.value
            const MODES_P_MIN = dataForHour.find((cell) => cell.column === 'MODES_P_MIN')?.value
            const MODES_P_MAX = dataForHour.find((cell) => cell.column === 'MODES_P_MAX')?.value
            const MODES_DECLARED = dataForHour.find((cell) => cell.column === 'MODES_DECLARED')?.value
            const P_GEN = dataForHour.find((cell) => cell.column === 'P_GEN')?.value ?? ''
            const AVRCHM_LOAD = FLOOD_MODE_WATCH //|| IS_ACTIVE_FLOOD_MODE_WATCH
              ? 0
              : (dataForHour.find((cell) => cell.column === 'AVRCHM_LOAD')?.value ?? 0)
            const NPRCH = dataForHour.find((cell) => cell.column === 'NPRCH')?.value

            const limit_min_cell = calcCellFromAlphabet(
              cursor + (plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + 2 * indexRgu + 2 + 1,
            )
            const limit_max_cell = calcCellFromAlphabet(
              cursor + (plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + 2 * indexRgu + 3 + 1,
            )
            const avrchm_cell = calcCellFromAlphabet(cursor + (plantRgus.length + 1) * 3 + 2 * indexRgu + 3 + 1)
            const rm_min = calcCellFromAlphabet(
              cursor +
                (plantRgus.length + 1) * 3 +
                (plantRgus.length + 1) * 2 +
                (plantRgus.length + 1) * 2 +
                2 * indexRgu +
                2 +
                1,
            )
            const rm_max = calcCellFromAlphabet(
              cursor +
                (plantRgus.length + 1) * 3 +
                (plantRgus.length + 1) * 2 +
                (plantRgus.length + 1) * 2 +
                2 * indexRgu +
                3 +
                1,
            )
            const modes_min = calcCellFromAlphabet(
              cursor +
                (plantRgus.length + 1) * 3 +
                (plantRgus.length + 1) * 2 +
                (plantRgus.length + 1) * 2 +
                (plantRgus.length + 1) * 2 +
                2 * indexRgu +
                2 +
                1,
            )
            const modes_max = calcCellFromAlphabet(
              cursor +
                (plantRgus.length + 1) * 3 +
                (plantRgus.length + 1) * 2 +
                (plantRgus.length + 1) * 2 +
                (plantRgus.length + 1) * 2 +
                2 * indexRgu +
                2 +
                2,
            )

            const zones =
              plantRgus[indexRgu] && plantRgus[indexRgu]?.allowedZones[index]?.zones
                ? plantRgus[indexRgu]?.allowedZones[index]?.zones
                : []
            const P_MAX_RESULT = makePMaxFormula(
              zones,
              `ROUND(${limit_max_cell}${index + 1}-${avrchm_cell}${index + 1},3)`,
            )
            const key_P_MAX_RGU = calcCellFromAlphabet(cursor + 6 + indexRgu * 3)
            const P_MIN_RESULT = FLOOD_MODE_WATCH
              ? makePMinFormula(zones, `ROUND(MAX(0,${key_P_MAX_RGU}${index + 1} - 1),3)`)
              : makePMinFormula(zones, `ROUND(MAX(0,${limit_min_cell}${index + 1}+${avrchm_cell}${index + 1}),3)`)

            const RESERV_MAX = dataForHour?.find((cell) => cell.column === 'RESERVES_MAX')?.value ?? ''

            const P_MIN = `=ROUND(MAX(${rm_min}${index + 1},${modes_min}${index + 1}),3)`
            const P_MAX = `=ROUND(MIN(${rm_max}${index + 1},${modes_max}${index + 1}),3)`

            firstRgu = [...firstRgu, P_MIN_RESULT, P_GEN, P_MAX_RESULT]
            secondRgu = [...secondRgu, RESERV_MAX, AVRCHM_LOAD, NPRCH]
            thirdRgu = [...thirdRgu, P_MIN, P_MAX]
            fourthRgu = [...fourthRgu, CM_P_MIN, CM_P_MAX]
            fifthRgu = [...fifthRgu, MODES_P_MIN, MODES_P_MAX, MODES_DECLARED]
          })
          const key_P_MIN_RESULT = calcCellFromAlphabet(cursor + 3)
          const getPminResult = () => {
            if (FLOOD_MODE_WATCH) {
              if (REGULATED_UNIT === 'PLANT') {
                return makePMinFormula(plantZones, `ROUND(MAX(0,(${key_P_MIN_RESULT}${index + 1} - 1)),3)`)
              } else {
                const rgusForPminString = rgusForPmin.join('+')

                return makePMinFormula(plantZones, `ROUND(MAX(0,${rgusForPminString}),3)`)
              }
            } else {
              return makePMinFormula(
                plantZones,
                `ROUND(MAX(0,${limit_min_cell}${index + 1}+${avrchm_cell}${index + 1}),3)`,
              )
            }
          }
          const getPmaxResult = () => {
            if (FLOOD_MODE_WATCH) {
              if (REGULATED_UNIT === 'PLANT') {
                return makePMaxFormula(plantZones, `ROUND(${limit_max_cell}${index + 1}-${avrchm_cell}${index + 1},3)`)
              } else {
                const rgusForPmaxString = rgusForPmax.join('+')

                return makePMaxFormula(plantZones, `ROUND(${rgusForPmaxString},3)`)
              }
            } else {
              return makePMaxFormula(plantZones, `ROUND(${limit_max_cell}${index + 1}-${avrchm_cell}${index + 1},3)`)
            }
          }
          const P_MIN_RESULT = getPminResult()
          const P_MAX_RESULT = getPmaxResult()
          dataPerStation = [
            P_MIN_RESULT,
            P_GEN_RESULT,
            P_MAX_RESULT,

            ...firstRgu,

            RESERV_MAX,
            AVRCHM_LOAD,
            NPRCH,

            ...secondRgu,

            P_MIN,
            P_MAX,

            ...thirdRgu,

            CM_P_MIN,
            CM_P_MAX,

            ...fourthRgu,

            MODES_P_MIN,
            MODES_P_MAX,
            MODES_DECLARED,

            ...fifthRgu,
          ]
          cursor = cursor + dataPerStation.length
          dataTempHour = [...dataTempHour, ...dataPerStation]
        })
        dataTemp = [...dataTemp, dataTempHour]
      })
      let collapsibleIndex = 0
      plantsListForAside
        .filter((el) => el.mixing)
        .forEach((el) => {
          const plant = plantsData.find((ps) => ps.plantId === el.plantId)
          if (!plant) return
          const plantRgus = plant.rgus
          const accepted = plant.accepted
          collapsibleColumnsRes = [...collapsibleColumnsRes, { row: -5, col: collapsibleIndex, collapsible: true }]
          if (plantRgus.length > 0) {
            collapsibleColumnsRes = [
              {
                row: -4,
                col: collapsibleIndex,
                collapsible: true,
              },
              {
                row: -4,
                col: collapsibleIndex + 3 + plantRgus.length * 3,
                collapsible: true,
              },
              {
                row: -4,
                col: collapsibleIndex + 5 + plantRgus.length * 3 + plantRgus.length * 2,
                collapsible: true,
              },
              {
                row: -4,
                col: collapsibleIndex + 7 + plantRgus.length * 3 + plantRgus.length * 4,
                collapsible: true,
              },
              {
                row: -4,
                col: collapsibleIndex + 9 + plantRgus.length * 3 + plantRgus.length * 6,
                collapsible: true,
              },
              ...collapsibleColumnsRes,
            ]
          }
          collapsibleIndex = collapsibleIndex + (plantRgus.length + 1) * 11
          const resFirst = [
            {
              label: `${el.name}`,
              colspan: (plantRgus.length + 1) * 11,
            },
          ]
          const resSecond = [
            { label: 'Итог', colspan: 3 + plantRgus.length * 3 },
            { label: 'Резервы', colspan: 2 + plantRgus.length * 2 },
            {
              label: 'Итог.ОГР',
              colspan: 2 + plantRgus.length * 2,
            },
            { label: 'РМ', colspan: 2 + plantRgus.length * 2 },
            { label: 'Модес', colspan: 2 + plantRgus.length * 2 },
          ]
          let firstRgu: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let secondRgu: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let thirdRgu: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let fourthRgu: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let fifthRgu: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          plantRgus.forEach(() => {
            firstRgu = [
              ...firstRgu,
              {
                label: 'мин',
                colspan: 1,
                accepted: plant.accepted,
              },
              {
                label: 'план',
                colspan: 1,
                accepted: plant.accepted,
              },
              {
                label: 'макс',
                colspan: 1,
                accepted: plant.accepted,
              },
            ]
            secondRgu = [
              ...secondRgu,
              {
                label: 'Rмакс',
                colspan: 1,
                accepted: plant.accepted,
              },
              {
                label: 'АВРЧМ',
                colspan: 1,
                accepted: plant.accepted,
              },
            ]
            thirdRgu = [
              ...thirdRgu,
              {
                label: 'мин',
                colspan: 1,
                accepted: plant.accepted,
              },
              {
                label: 'макс',
                colspan: 1,
                accepted: plant.accepted,
              },
            ]
            fourthRgu = [
              ...fourthRgu,
              {
                label: 'мин',
                colspan: 1,
                accepted: plant.accepted,
              },
              {
                label: 'макс',
                colspan: 1,
                accepted: plant.accepted,
              },
            ]
            fifthRgu = [
              ...fifthRgu,
              {
                label: 'мин',
                colspan: 1,
                accepted: plant.accepted,
              },
              {
                label: 'макс',
                colspan: 1,
                accepted: plant.accepted,
              },
            ]
          })
          const resThird = [
            { label: 'Σ', colspan: 3 },
            ...plantRgus.map((el) => ({
              label: el.rguName,
              colspan: 3,
            })),
            { label: 'Σ', colspan: 2 },
            ...plantRgus.map((el) => ({
              label: el.rguName,
              colspan: 2,
            })),
            { label: 'Σ', colspan: 2 },
            ...plantRgus.map((el) => ({
              label: el.rguName,
              colspan: 2,
            })),
            { label: 'Σ', colspan: 2 },
            ...plantRgus.map((el) => ({
              label: el.rguName,
              colspan: 2,
            })),
            { label: 'Σ', colspan: 2 },
            ...plantRgus.map((el) => ({
              label: el.rguName,
              colspan: 2,
            })),
          ]
          const resFourth: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = [
            { label: 'мин', colspan: 1, accepted: plant.accepted },
            { label: 'план', colspan: 1, accepted: plant.accepted },
            { label: 'макс', colspan: 1, accepted: plant.accepted },
            ...firstRgu,
            {
              label: 'Rмакс',
              colspan: 1,
              accepted: plant.accepted,
            },
            {
              label: 'АВРЧМ',
              colspan: 1,
              accepted: plant.accepted,
            },
            ...secondRgu,
            { label: 'мин', colspan: 1, accepted: plant.accepted },
            { label: 'макс', colspan: 1, accepted: plant.accepted },
            ...thirdRgu,
            { label: 'мин', colspan: 1, accepted: plant.accepted },
            { label: 'макс', colspan: 1, accepted: plant.accepted },
            ...fourthRgu,
            { label: 'мин', colspan: 1, accepted: plant.accepted },
            { label: 'макс', colspan: 1, accepted: plant.accepted },
            ...fifthRgu,
          ]
          let rguStageFirstBlock: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let rguStageSecondBlock: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let rguStageThirdBlock: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let rguStageFourthBlock: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          let rguStageFifthBlock: IVaultStore['vaultSpreadsheet']['nestedHeaders'][0] = []
          plantRgus.forEach((el) => {
            rguStageFirstBlock = [
              ...rguStageFirstBlock,
              {
                label: PlanningStageRussia[el.columnStages['RESULT_MIN']],
                colspan: 1,
                accepted,
                code: el.columnStages['RESULT_MIN'],
              },
              {
                label: PlanningStageRussia[el.columnStages['P_GEN']],
                colspan: 1,
                accepted,
                code: el.columnStages['P_GEN'],
              },
              {
                label: PlanningStageRussia[el.columnStages['RESULT_MAX']],
                colspan: 1,
                accepted,
                code: el.columnStages['RESULT_MAX'],
              },
            ]
            rguStageSecondBlock = [
              ...rguStageSecondBlock,
              {
                label: PlanningStageRussia[el.columnStages['RESERVES_MAX']],
                colspan: 1,
                accepted,
                code: el.columnStages['RESERVES_MAX'],
              },
              {
                label: PlanningStageRussia[el.columnStages['AVRCHM_LOAD']],
                colspan: 1,
                accepted,
                code: el.columnStages['AVRCHM_LOAD'],
              },
            ]
            rguStageThirdBlock = [
              ...rguStageThirdBlock,
              {
                label: PlanningStageRussia[el.columnStages['LIMIT_MIN']],
                colspan: 1,
                accepted,
                code: el.columnStages['LIMIT_MIN'],
              },
              {
                label: PlanningStageRussia[el.columnStages['LIMIT_MAX']],
                colspan: 1,
                accepted,
                code: el.columnStages['LIMIT_MAX'],
              },
            ]
            rguStageFourthBlock = [
              ...rguStageFourthBlock,
              {
                label: PlanningStageRussia[el.columnStages['CM_P_MIN']],
                colspan: 1,
                accepted,
                code: el.columnStages['CM_P_MIN'],
              },
              {
                label: PlanningStageRussia[el.columnStages['CM_P_MAX']],
                colspan: 1,
                accepted,
                code: el.columnStages['CM_P_MAX'],
              },
            ]
            rguStageFifthBlock = [
              ...rguStageFifthBlock,
              {
                label: PlanningStageRussia[el.columnStages['MODES_P_MIN']],
                colspan: 1,
                accepted,
                code: el.columnStages['MODES_P_MIN'],
              },
              {
                label: PlanningStageRussia[el.columnStages['MODES_P_MAX']],
                colspan: 1,
                accepted,
                code: el.columnStages['MODES_P_MAX'],
              },
            ]
          })
          const resFifth = [
            {
              label: PlanningStageRussia[plant.columnStages['RESULT_MIN']],
              colspan: 1,
              accepted,
              code: plant.columnStages['RESULT_MIN'],
            },
            {
              label: PlanningStageRussia[plant.columnStages['P_GEN']],
              colspan: 1,
              accepted,
              code: plant.columnStages['P_GEN'],
            },
            {
              label: PlanningStageRussia[plant.columnStages['RESULT_MAX']],
              colspan: 1,
              accepted,
              code: plant.columnStages['RESULT_MAX'],
            },
            ...rguStageFirstBlock,
            {
              label: PlanningStageRussia[plant.columnStages['RESERVES_MAX']],
              colspan: 1,
              accepted,
              code: plant.columnStages['RESERVES_MAX'],
            },
            {
              label: PlanningStageRussia[plant.columnStages['AVRCHM_LOAD']],
              colspan: 1,
              accepted,
              code: plant.columnStages['AVRCHM_LOAD'],
            },
            ...rguStageSecondBlock,
            {
              label: PlanningStageRussia[plant.columnStages['LIMIT_MIN']],
              colspan: 1,
              accepted,
              code: plant.columnStages['LIMIT_MIN'],
            },
            {
              label: PlanningStageRussia[plant.columnStages['LIMIT_MAX']],
              colspan: 1,
              accepted,
              code: plant.columnStages['LIMIT_MAX'],
            },
            ...rguStageThirdBlock,
            {
              label: PlanningStageRussia[plant.columnStages['CM_P_MIN']],
              colspan: 1,
              accepted,
              code: plant.columnStages['CM_P_MIN'],
            },
            {
              label: PlanningStageRussia[plant.columnStages['CM_P_MAX']],
              colspan: 1,
              accepted,
              code: plant.columnStages['CM_P_MAX'],
            },
            ...rguStageFourthBlock,
            {
              label: PlanningStageRussia[plant.columnStages['MODES_P_MIN']],
              colspan: 1,
              accepted,
              code: plant.columnStages['MODES_P_MIN'],
            },
            {
              label: PlanningStageRussia[plant.columnStages['MODES_P_MAX']],
              colspan: 1,
              accepted,
              code: plant.columnStages['MODES_P_MAX'],
            },
            ...rguStageFifthBlock,
          ]
          nestedHeadersFirst = [...nestedHeadersFirst, ...resFirst]
          nestedHeadersSecond = [...nestedHeadersSecond, ...resSecond]
          nestedHeadersThird = [...nestedHeadersThird, ...resThird]
          nestedHeadersFourth = [...nestedHeadersFourth, ...resFourth]
          nestedHeadersFifth = [...nestedHeadersFifth, ...resFifth]
          colNumberPerStation = nestedHeadersFirst.map((el) => el.colspan)
        })
      nestedHeaders = [
        nestedHeadersFirst,
        nestedHeadersSecond,
        nestedHeadersThird,
        nestedHeadersFourth,
        nestedHeadersFifth,
      ]
      let customHeaderUp: IVaultStore['vaultSpreadsheet']['customHeaders'] = []
      nestedHeadersFifth.forEach((el, index) => {
        const accepted = el?.accepted
        const className = accepted ? 'acceptedBgColor' : ''
        const finished = this._planningStage !== el.code
        customHeaderUp = [
          ...customHeaderUp,
          {
            col: index,
            className,
            stageClassName: el.code ? getColorStage(el.code, finished) : '',
            code: el.code,
            thText: el.code ? PlanningStageRussia[el.code] : undefined,
          },
        ]
      })
      customHeaders = customHeaderUp
      let columnTemp: IVaultStore['vaultSpreadsheet']['column'] = []
      stations.forEach((item) => {
        const plant = plantsData.find((ps) => ps.plantId === item.plantId)
        const accepted = plant?.accepted ?? false
        const FLOOD_MODE_WATCH = plant?.inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false
        const plantRgus = plantsData.find((ps) => ps.plantId === item.plantId)?.rgus ?? []
        let firstRgu: IVaultStore['vaultSpreadsheet']['column'] = []
        let secondRgu: IVaultStore['vaultSpreadsheet']['column'] = []
        let thirdRgu: IVaultStore['vaultSpreadsheet']['column'] = []
        let fourthRgu: IVaultStore['vaultSpreadsheet']['column'] = []
        let fifthRgu: IVaultStore['vaultSpreadsheet']['column'] = []
        const disabled = plant?.accepted || viewOnly || isLastDay || this._isFinishStage || !editMode
        const secondRguColumns = disabled
          ? [
              { editor: false, readOnly: false },
              { editor: false, readOnly: false },
            ]
          : [
              { editor: false, readOnly: false },
              FLOOD_MODE_WATCH ? { editor: false, readOnly: false } : { editor: 'numeric' },
            ]
        plantRgus.forEach(() => {
          firstRgu = [
            ...firstRgu,
            { editor: false, readOnly: false },
            { editor: 'numeric', readOnly: false },
            { editor: false, readOnly: false },
          ]
          secondRgu = [...secondRgu, ...secondRguColumns]
          thirdRgu = [...thirdRgu, { editor: false, readOnly: false }, { editor: false, readOnly: false }]
          fourthRgu = [...fourthRgu, { editor: 'numeric', readOnly: false }, { editor: 'numeric', readOnly: false }]
          fifthRgu = [...fifthRgu, { editor: false, readOnly: false }, { editor: false, readOnly: false }]
        })
        if (accepted || item.viewOnly || viewOnly || isLastDay || !editMode || this._isFinishStage) {
          columnTemp = [
            ...columnTemp,
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            ...firstRgu.map(() => ({
              editor: false,
              readOnly: false,
            })),
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            ...secondRgu.map(() => ({
              editor: false,
              readOnly: false,
            })),
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            ...thirdRgu.map(() => ({
              editor: false,
              readOnly: false,
            })),
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            ...fourthRgu.map(() => ({
              editor: false,
              readOnly: false,
            })),
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            ...fifthRgu.map(() => ({
              editor: false,
              readOnly: false,
            })),
          ]
        } else {
          columnTemp = [
            ...columnTemp,
            { editor: false, readOnly: false },
            { editor: 'numeric', readOnly: false },
            { editor: false, readOnly: false },
            ...firstRgu,
            { editor: false, readOnly: false },
            FLOOD_MODE_WATCH ? { editor: false, readOnly: false } : { editor: 'numeric', readOnly: false },
            ...secondRgu,
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            ...thirdRgu,
            { editor: 'numeric', readOnly: false },
            { editor: 'numeric', readOnly: false },
            ...fourthRgu,
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            ...fifthRgu,
          ]
        }
      })
      column = columnTemp.map((el) => ({
        ...el,
      }))
      collapsibleColumns = collapsibleColumnsRes
      data = dataTemp
      const headersMap: { [key: number]: number } = {}
      let headersMapKeys: string[] = []
      let cursor: number = 0
      nestedHeadersFirst.forEach((el, index) => {
        const plantId = stations[index]?.value ?? null
        const plantRgus = plantsData.find((ps) => ps.plantId === plantId)?.rgus ?? []
        const colspan = el.colspan
        const firstRgu: string[] = []
        const secondRgu: string[] = []
        const thirdRgu: string[] = []
        const fourthRgu: string[] = []
        const fifthRgu: string[] = []

        plantRgus.forEach((rgu) => {
          firstRgu.push(`${rgu.rguId}-${keysGES[0]}`)
          firstRgu.push(`${rgu.rguId}-${keysGES[1]}`)
          firstRgu.push(`${rgu.rguId}-${keysGES[2]}`)

          secondRgu.push(`${rgu.rguId}-${keysGES[3]}`)
          secondRgu.push(`${rgu.rguId}-${keysGES[4]}`)

          thirdRgu.push(`${rgu.rguId}-${keysGES[5]}`)
          thirdRgu.push(`${rgu.rguId}-${keysGES[6]}`)

          fourthRgu.push(`${rgu.rguId}-${keysGES[7]}`)
          fourthRgu.push(`${rgu.rguId}-${keysGES[8]}`)

          fifthRgu.push(`${rgu.rguId}-${keysGES[9]}`)
          fifthRgu.push(`${rgu.rguId}-${keysGES[10]}`)
        })

        headersMapKeys = [
          ...headersMapKeys,
          keysGES[0],
          keysGES[1],
          keysGES[2],
          ...firstRgu,
          keysGES[3],
          keysGES[4],
          ...secondRgu,
          keysGES[5],
          keysGES[6],
          ...thirdRgu,
          keysGES[7],
          keysGES[8],
          ...fourthRgu,
          keysGES[9],
          keysGES[10],
          ...fifthRgu,
        ]
        for (let i = 0; i < colspan; i++) {
          headersMap[cursor] = plantId
          cursor = cursor + 1
        }
      })
      let resCell: IVaultStore['vaultSpreadsheet']['cell'] = []
      dataTemp.forEach((row, x) => {
        let numberOfColumnsPassedByStations = 0
        const listOfIdsPassedByStations: number[] = []
        resCell = [
          ...resCell,
          ...row
            .map((_, y) => {
              const dataHour = vaultData[x]
              const idStation = headersMap[y]
              if (!listOfIdsPassedByStations.includes(Number(idStation))) {
                listOfIdsPassedByStations.push(idStation)
                numberOfColumnsPassedByStations = y
              }
              const keyStation = headersMapKeys[y] as typeKeysGes
              const plantRgus = plantsData.find((ps) => ps.plantId === idStation)?.rgus ?? []
              const plant = plantsData.find((ps) => ps.plantId === idStation)
              const REGULATED_UNIT = plant?.regulatedUnit ?? RegistryType.PLANT
              let object: ITempGesCell = {
                P_MIN_RESULT: 0,
                P_GEN: 0,
                P_MAX_RESULT: 0,
                RESERVES_MAX: 0,
                AVRCHM_LOAD: 0,
                NPRCH: 0,
                P_MIN: 0,
                P_MAX: 0,
                CM_P_MIN: 0,
                CM_P_MAX: 0,
                MODES_P_MIN: 0,
                MODES_P_MAX: 0,
                MODES_DECLARED: 0,
                CONSUMPT: 0,
                allowedZones: [],
              }
              let isValid: boolean = false
              let comment: string | undefined
              let value: string | number | null | undefined = 0
              const typeIsRgu = isRgu(keyStation)
              const typeObj = typeIsRgu ? 'RGU' : 'PLANT'
              const P_GEN_PLANT = vaultData[x][`${idStation}-P_GEN`] ? Number(vaultData[x][`${idStation}-P_GEN`]) : 0
              const AVRCHM_LOAD_PLANT = vaultData[x][`${idStation}-AVRCHM_LOAD`]
                ? Number(vaultData[x][`${idStation}-AVRCHM_LOAD`])
                : 0
              const P_MIN_PLANT = vaultData[x][`${idStation}-P_MIN`] ? Number(vaultData[x][`${idStation}-P_MIN`]) : 0
              const P_MAX_PLANT = vaultData[x][`${idStation}-P_MAX`] ? Number(vaultData[x][`${idStation}-P_MAX`]) : 0
              const CM_P_MIN_PLANT = vaultData[x][`${idStation}-CM_P_MIN`]
                ? Number(vaultData[x][`${idStation}-CM_P_MIN`])
                : 0
              const CM_P_MAX_PLANT = vaultData[x][`${idStation}-CM_P_MAX`]
                ? Number(vaultData[x][`${idStation}-CM_P_MAX`])
                : 0
              //
              const rgusArr = plantsData.find((el) => el.plantId === idStation)?.rgus ?? []
              const P_GEN_RGUS = rgusArr.reduce(
                (acc, cur) => acc + (cur.rows[x].cells.find((c) => c.column === 'P_GEN')?.value ?? 0),
                0,
              )
              const AVRCHM_LOAD_RGUS = rgusArr.reduce(
                (acc, cur) => acc + (cur.rows[x].cells.find((c) => c.column === 'AVRCHM_LOAD')?.value ?? 0),
                0,
              )
              const P_MIN_RGUS = rgusArr.reduce(
                (acc, cur) => acc + (cur.rows[x].cells.find((c) => c.column === 'LIMIT_MIN')?.value ?? 0),
                0,
              )
              const P_MAX_RGUS = rgusArr.reduce(
                (acc, cur) => acc + (cur.rows[x].cells.find((c) => c.column === 'LIMIT_MAX')?.value ?? 0),
                0,
              )
              const CM_P_MIN_RGUS = rgusArr.reduce(
                (acc, cur) => acc + (cur.rows[x].cells.find((c) => c.column === 'CM_P_MIN')?.value ?? 0),
                0,
              )
              const CM_P_MAX_RGUS = rgusArr.reduce(
                (acc, cur) => acc + (cur.rows[x].cells.find((c) => c.column === 'CM_P_MAX')?.value ?? 0),
                0,
              )
              const IS_VIEW_CM_P_MIN = rgusArr.some((rgu) => {
                return rgu.rows[x].cells.some((el) => el.column === 'CM_P_MIN')
              })
              const IS_VIEW_CM_P_MAX = rgusArr.some((rgu) => {
                return rgu.rows[x].cells.some((el) => el.column === 'CM_P_MAX')
              })
              const validObject = {
                is_Valid_P_GEN: rgusArr.length > 0 && Number(P_GEN_PLANT.toFixed(3)) !== Number(P_GEN_RGUS.toFixed(3)),
                is_Valid_AVRCHM_LOAD:
                  rgusArr.length > 0 && Number(AVRCHM_LOAD_PLANT.toFixed(3)) !== Number(AVRCHM_LOAD_RGUS.toFixed(3)),
                is_Valid_P_MIN: rgusArr.length > 0 && Number(P_MIN_PLANT.toFixed(3)) < Number(P_MIN_RGUS.toFixed(3)),
                is_Valid_P_MAX: rgusArr.length > 0 && Number(P_MAX_PLANT.toFixed(3)) > Number(P_MAX_RGUS.toFixed(3)),
                is_Valid_CM_P_MIN:
                  IS_VIEW_CM_P_MIN &&
                  Number(CM_P_MIN_PLANT !== undefined ? CM_P_MIN_PLANT.toFixed(3) : 0) <
                    Number(CM_P_MIN_RGUS !== undefined ? CM_P_MIN_RGUS.toFixed(3) : 0),
                is_Valid_CM_P_MAX:
                  IS_VIEW_CM_P_MAX &&
                  Number(CM_P_MAX_PLANT !== undefined ? CM_P_MAX_PLANT.toFixed(3) : 0) >
                    Number(CM_P_MAX_RGUS !== undefined ? CM_P_MAX_RGUS.toFixed(3) : 0),
              }
              let idRGU: number | null = null
              let inputResultProps: IInputResultItemProp | undefined
              const inputResultStartColumnIndexes = colNumberPerStation?.reduce((acc, stationColNum, idx) => {
                if (acc.length === 0) {
                  acc.push(stationColNum)
                } else {
                  acc.push(acc[idx - 1] + stationColNum)
                }

                return acc
              }, [] as number[])
              inputResultStartColumnIndexes.forEach((stationColNum, idx) => {
                if (
                  (idx === 0 && y < stationColNum) ||
                  (idx > 0 && y >= inputResultStartColumnIndexes[idx - 1] && y < stationColNum)
                ) {
                  inputResultProps = {
                    stationColNum: colNumberPerStation[idx],
                    wMin: {
                      comment: undefined,
                      active: false,
                      disabled: true,
                      value: this.inputValues[idx].W_MIN,
                      isValid: false,
                    },
                    wMax: {
                      comment: undefined,
                      active: false,
                      disabled: true,
                      value: this.inputValues[idx].W_MAX,
                      isValid: false,
                    },
                    pGen: {
                      comment: undefined,
                      active: false,
                      disabled: true,
                      value: this.inputValues[idx].P_GEN_TARGET,
                      isValid: false,
                    },
                  }
                }
              })
              if (!typeIsRgu) {
                object = {
                  P_MIN_RESULT: dataHour[`${idStation}-P_MIN_RESULT`],
                  P_GEN: dataHour[`${idStation}-P_GEN`],
                  P_MAX_RESULT: dataHour[`${idStation}-P_MAX_RESULT`],
                  RESERVES_MAX:
                    dataHour[`${idStation}-RESERVES_MAX`] !== undefined ? dataHour[`${idStation}-RESERVES_MAX`] : '',
                  AVRCHM_LOAD: dataHour[`${idStation}-AVRCHM_LOAD`],
                  P_MIN: dataHour[`${idStation}-P_MIN`],
                  P_MAX: dataHour[`${idStation}-P_MAX`],
                  CM_P_MIN: dataHour[`${idStation}-CM_P_MIN`],
                  CM_P_MAX: dataHour[`${idStation}-CM_P_MAX`],
                  MODES_P_MIN: dataHour[`${idStation}-MODES_P_MIN`],
                  MODES_P_MAX: dataHour[`${idStation}-MODES_P_MAX`],
                  allowedZones: dataHour[`${idStation}-allowedZones`],
                } as ITempGesCell
                value = object[keyStation]
                if (keyStation === 'P_GEN' || keyStation === 'RESERVES_MAX') {
                  value = typeof value === 'string' ? (value.length > 0 ? value : 0) : value
                }
                const VALIDATE_MAIN = getValidGES(idStation, keyStation, object, value, inputResultProps)
                const VALIDATE_PLANT_WITH_RGU = additionalValidation(
                  typeObj,
                  object,
                  value,
                  keyStation,
                  REGULATED_UNIT,
                  validObject,
                )
                isValid = VALIDATE_MAIN === undefined && VALIDATE_PLANT_WITH_RGU === undefined
                const VALIDATE_MESSAGE_MAIN = VALIDATE_MAIN === undefined ? '' : VALIDATE_MAIN
                const VALIDATE_MESSAGE_PLANT_WITH_RGU =
                  VALIDATE_PLANT_WITH_RGU === undefined ? '' : VALIDATE_PLANT_WITH_RGU
                if (!isValid) {
                  comment = VALIDATE_MESSAGE_MAIN + VALIDATE_MESSAGE_PLANT_WITH_RGU
                }
              } else {
                const [id, key] = keyStation.split('-')
                const objRgu = plantRgus.find((el) => el.rguId === Number(id))
                if (objRgu) {
                  idRGU = objRgu.rguId
                }
                const cells = objRgu?.rows[x]?.cells ?? []
                const P_GEN = cells.find((item) => item.column === 'P_GEN')?.value ?? 0
                const CM_P_MIN = cells.find((item) => item.column === 'CM_P_MIN')
                const CM_P_MAX = cells.find((item) => item.column === 'CM_P_MAX')
                const MODES_P_MIN = cells.find((item) => item.column === 'MODES_P_MIN')
                const MODES_P_MAX = cells.find((item) => item.column === 'MODES_P_MAX')
                const FLOOD_MODE_WATCH = !!plant?.inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn
                const getPmax = () => {
                  if (CM_P_MAX && MODES_P_MAX) {
                    return Math.min(CM_P_MAX.value ?? 0, MODES_P_MAX.value ?? 0)
                  }
                  if (!CM_P_MAX && !MODES_P_MAX) {
                    return undefined
                  }
                  if (!CM_P_MAX) {
                    return MODES_P_MAX?.value ?? 0
                  }
                  if (!MODES_P_MAX) {
                    return CM_P_MAX?.value ?? 0
                  }
                }
                const pmax = getPmax()
                const P_MAX = pmax === undefined ? 0 : pmax < 0 ? 0 : pmax
                const getPmin = () => {
                  if (FLOOD_MODE_WATCH) {
                    return P_MAX - 1
                  } else {
                    if (CM_P_MIN && MODES_P_MIN) {
                      return Math.max(CM_P_MIN.value ?? 0, MODES_P_MIN.value ?? 0)
                    }
                    if (!CM_P_MIN && !MODES_P_MIN) {
                      return undefined
                    }
                    if (!CM_P_MIN) {
                      return MODES_P_MIN?.value ?? 0
                    }
                    if (!MODES_P_MIN) {
                      return CM_P_MIN?.value ?? 0
                    }
                  }
                }
                const pmin = getPmin()
                const P_MIN = pmin === undefined ? 0 : pmin < 0 ? 0 : pmin
                const AVRCHM_LOAD = cells.find((item) => item.column === 'AVRCHM_LOAD')?.value ?? 0
                const allowedZones =
                  objRgu?.allowedZones && objRgu.allowedZones.length > 0 ? objRgu?.allowedZones[x]?.zones : []
                const P_MAX_RESULT = makePMax(allowedZones || [], Number(P_MAX) - Number(AVRCHM_LOAD))
                const P_MIN_RESULT = makePMin(allowedZones || [], Number(P_MIN) + Number(AVRCHM_LOAD))
                const RESERVES_MAX = cells.find((item) => item.column === 'RESERVES_MAX')?.value ?? undefined
                object = {
                  P_MIN_RESULT,
                  P_GEN,
                  P_MAX_RESULT,
                  RESERVES_MAX: RESERVES_MAX !== undefined ? RESERVES_MAX : '',
                  AVRCHM_LOAD,
                  P_MIN,
                  P_MAX,
                  CONSUMPT: '',
                  CM_P_MIN: CM_P_MIN ? CM_P_MIN?.value : '',
                  CM_P_MAX: CM_P_MAX ? CM_P_MAX?.value : '',
                  MODES_P_MIN: MODES_P_MIN ? MODES_P_MIN?.value : '',
                  MODES_P_MAX: MODES_P_MAX ? MODES_P_MAX?.value : '',
                  allowedZones,
                }
                value = object[key as typeKeysGes]
                if (key === 'P_GEN' || key === 'RESERVES_MAX') {
                  value = typeof value === 'string' ? (value.length > 0 ? value : 0) : value
                }
                const VALIDATE_MAIN = getValidGES(idStation, key, object, value, inputResultProps)
                const VALIDATE_PLANT_WITH_RGU = additionalValidation(
                  typeObj,
                  object,
                  value,
                  key as typeKeysGes,
                  REGULATED_UNIT,
                  validObject,
                )
                isValid = VALIDATE_MAIN === undefined && VALIDATE_PLANT_WITH_RGU === undefined
                const VALIDATE_MESSAGE_MAIN = VALIDATE_MAIN === undefined ? '' : VALIDATE_MAIN
                const VALIDATE_MESSAGE_PLANT_WITH_RGU =
                  VALIDATE_PLANT_WITH_RGU === undefined ? '' : VALIDATE_PLANT_WITH_RGU
                if (!isValid) {
                  comment = VALIDATE_MESSAGE_MAIN + VALIDATE_MESSAGE_PLANT_WITH_RGU
                }
              }
              const maxConsumptionHour = plant ? plant.maxConsumptionHour - 1 : -2
              const minConsumptionHour = plant ? plant.minConsumptionHour - 1 : -2
              const isMaxConsumptionHour =
                maxConsumptionHour === x - 1 ||
                maxConsumptionHour === x ||
                (maxConsumptionHour === x + 1 && maxConsumptionHour !== -2)
              const isMinConsumptionHour =
                minConsumptionHour === x - 1 ||
                minConsumptionHour === x ||
                (minConsumptionHour === x + 1 && minConsumptionHour !== -2)
              let type = ''
              if (plant?.plantOptimized) {
                if (idRGU !== null) {
                  type = 'plantOptimizedRgu'
                } else {
                  type = 'plantOptimized'
                }
              } else {
                if (idRGU !== null) {
                  type = 'plantNotOptimizedRgu'
                } else {
                  type = 'plantNotOptimized'
                }
              }
              const cell = getGesCellPropByTableCoords(
                plant?.rows || [],
                rgusArr,
                x,
                y - numberOfColumnsPassedByStations,
              )
              const renderer = getStatusCell(
                isMaxConsumptionHour,
                isMinConsumptionHour,
                columnTemp[y]?.editor,
                keyStation,
                type,
                isValid,
                cell?.manual || cell?.fixed,
              )

              return {
                row: x,
                col: y,
                renderer,
                isMaxConsumptionHour,
                isMinConsumptionHour,
                editor: columnTemp[y]?.editor,
                type,
                idStation,
                idRGU,
                keyStation,
                manual: cell?.manual,
                fixed: cell?.fixed,
                allowedZones: object['allowedZones'],
                comment: isValid ? undefined : getStyledComment(comment),
              }
            })
            .filter((el) => el.renderer !== 'cell'),
        ]
      })
      cell = resCell
    }
    runInAction(() => {
      this.vaultSpreadsheet = {
        inputResultProps: this.vaultSpreadsheet.inputResultProps,
        colNumberPerStation,
        nestedHeaders,
        customHeaders,
        column,
        collapsibleColumns,
        data,
        cell,
      }
      this.originalVaultSpreadsheet = klona(this.vaultSpreadsheet)
      this.recalculateInputValues()
    })
  }

  validate = (spreadsheetData: IVaultStore['vaultSpreadsheet']['data'], changes: Handsontable.CellChange[] | null) => {
    // Вычисляем значения по формулам и данным, которые хранятся в vaultSpreadsheet.data
    const hfPlugin = this.handsontableInstance?.getPlugin('formulas')
    const hfEngine = hfPlugin?.engine
    let dataCalculatedByFormulas: IVaultStore['vaultSpreadsheet']['data'] = []
    if (hfEngine) {
      // Такая обработка данных работает в 10 раз быстрее чем вызов метода this.handsontableInstance.getData()
      // ~20ms против ~200ms
      dataCalculatedByFormulas = spreadsheetData.map((row) =>
        row.map((value) => {
          if (typeof hfPlugin.sheetId === 'number' && typeof value === 'string' && value.includes('=')) {
            return hfEngine.calculateFormula(value, hfPlugin.sheetId) as number
          }

          return value
        }),
      )
    }

    // Валидируем значения на основе данных dataCalculatedByFormulas
    if (dataCalculatedByFormulas.length > 0) {
      const stations = this._plantsListForAside.filter((el) => el.mixing)
      const headersMap: { [key: number]: number } = {}
      const headersMapKeys = generateHeaderKeyIndexMap(stations, this.plantsData)
      let cursor: number = 0
      this.vaultSpreadsheet.nestedHeaders[0]?.forEach((el, index) => {
        const plantId = stations[index]?.value ?? null
        const colspan = el.colspan
        for (let i = 0; i < colspan; i++) {
          headersMap[cursor] = plantId
          cursor = cursor + 1
        }
      })
      const plantsIds = new Set<number>()
      changes?.forEach((change) => {
        const [row, col] = change
        const cellIdx = Number(row) * this.vaultSpreadsheet.column.length + Number(col)
        const el = this.vaultSpreadsheet.cell[cellIdx]
        if (Number(row) !== Number(el?.row) || Number(col) !== Number(el?.col)) {
          return el
        } else {
          plantsIds.add(el.idStation)
        }
      })
      this.vaultSpreadsheet.cell = this.vaultSpreadsheet.cell.map((el) => {
        const keys = Object.keys(headersMapKeys)[el.col]
        if (keys === undefined || (!plantsIds.has(Number(el.idStation)) && changes !== null)) {
          return {
            ...el,
          }
        }
        const [type, id, key, plantId] = keys.split('-')
        const isAvrchmKey = key === 'AVRCHM_LOAD'
        const FLOOD_MODE_WATCH = this.floodsLeft.some((id) => id === Number(plantId))
        const plant = this._plantsListForAside.find((ps) => ps.plantId === Number(plantId))
        let object: ITempGesCell = {
          P_MIN_RESULT: 0,
          P_GEN: 0,
          P_MAX_RESULT: 0,
          RESERVES_MAX: 0,
          AVRCHM_LOAD: 0,
          NPRCH: 0,
          P_MIN: 0,
          P_MAX: 0,
          CM_P_MIN: 0,
          CM_P_MAX: 0,
          MODES_P_MIN: 0,
          MODES_P_MAX: 0,
          MODES_DECLARED: 0,
          CONSUMPT: 0,
          allowedZones: [],
        }
        const keyStation: typeKeysGes = key as typeKeysGes
        const P_GEN = Number(dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-P_GEN-${plantId}`]])
        const cm_min = dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-CM_P_MIN-${plantId}`]]
        const cm_max = dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-CM_P_MAX-${plantId}`]]
        const modes_min = dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-MODES_P_MIN-${plantId}`]]
        const modes_max = dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-MODES_P_MAX-${plantId}`]]
        const CM_P_MIN = cm_min === undefined || cm_min === '' || cm_min === null ? undefined : Number(cm_min)
        const CM_P_MAX = cm_max === undefined || cm_max === '' || cm_max === null ? undefined : Number(cm_max)
        const MODES_P_MIN = modes_min === undefined || modes_min === '' ? undefined : Number(modes_min)
        const MODES_P_MAX = modes_max === undefined || modes_max === '' ? undefined : Number(modes_max)
        const P_MIN = Number(dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-LIMIT_MIN-${plantId}`]])
        const P_MAX = Number(dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-LIMIT_MAX-${plantId}`]])
        const AVRCHM_LOAD = Number(
          dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-AVRCHM_LOAD-${plantId}`]],
        )
        const nprch = dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-NPRCH-${plantId}`]]
        const modes_declared =
          dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-MODES_DECLARED-${plantId}`]]
        const NPRCH = nprch === undefined || nprch === '' || nprch === null ? undefined : Number(nprch)
        const MODES_DECLARED =
          modes_declared === undefined || modes_declared === '' ? undefined : Number(modes_declared)
        const CONSUMPT = Number(dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-CONSUMPT-${plantId}`]])
        const P_MIN_RESULT = Number(
          dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-P_MIN_RESULT-${plantId}`]],
        )
        const P_MAX_RESULT = Number(
          dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-P_MAX_RESULT-${plantId}`]],
        )
        const RESERVES_MAX = Number(
          dataCalculatedByFormulas[el.row][headersMapKeys[`${type}-${id}-RESERVES_MAX-${plantId}`]],
        )
        object = {
          P_MIN_RESULT,
          P_GEN,
          P_MAX_RESULT,
          RESERVES_MAX,
          AVRCHM_LOAD,
          NPRCH: NPRCH === undefined ? undefined : NPRCH,
          P_MIN,
          P_MAX,
          CM_P_MIN: CM_P_MIN === undefined ? undefined : CM_P_MIN,
          CM_P_MAX: CM_P_MAX === undefined ? undefined : CM_P_MAX,
          MODES_P_MIN: MODES_P_MIN === undefined ? undefined : MODES_P_MIN,
          MODES_P_MAX: MODES_P_MAX === undefined ? undefined : MODES_P_MAX,
          MODES_DECLARED: MODES_DECLARED === undefined ? undefined : MODES_DECLARED,
          CONSUMPT,
          allowedZones: el.allowedZones,
        }
        const value = object[keyStation]

        const P_GEN_RGU_ARR: number[] = []
        const AVRCHM_LOAD_ARR: number[] = []
        const P_MIN_ARR: number[] = []
        const P_MAX_ARR: number[] = []
        const CM_P_MIN_ARR: number[] = []
        const CM_P_MAX_ARR: number[] = []
        let P_GEN_PLANT = 0
        let AVRCHM_LOAD_PLANT = 0
        let P_MIN_PLANT = 0
        let P_MAX_PLANT = 0
        let CM_P_MIN_PLANT = 0
        let CM_P_MAX_PLANT = 0
        Object.entries(headersMapKeys).forEach(([elKey, index]) => {
          const [typeEl, _, keyEl, plantIdEl] = elKey.split('-')
          if (typeEl === 'PLANT') {
            if (plantIdEl === plantId) {
              const value = dataCalculatedByFormulas[el.row][index]
                ? Number(dataCalculatedByFormulas[el.row][index])
                : 0
              switch (keyEl) {
                case 'P_GEN':
                  P_GEN_PLANT = value
                  break
                case 'AVRCHM_LOAD':
                  AVRCHM_LOAD_PLANT = value
                  break
                case 'P_MIN':
                  P_MIN_PLANT = value
                  break
                case 'P_MAX':
                  P_MAX_PLANT = value
                  break
                case 'CM_P_MIN':
                  CM_P_MIN_PLANT = value
                  break
                case 'CM_P_MAX':
                  CM_P_MAX_PLANT = value
                  break
                default:
                  break
              }
            }
          } else {
            if (plantIdEl === plantId) {
              const value =
                dataCalculatedByFormulas[el?.row][index] !== undefined &&
                dataCalculatedByFormulas[el?.row][index] !== ''
                  ? Number(dataCalculatedByFormulas[el?.row][index])
                  : undefined
              if (value !== undefined) {
                switch (keyEl) {
                  case 'P_GEN':
                    P_GEN_RGU_ARR.push(value)
                    break
                  case 'AVRCHM_LOAD':
                    AVRCHM_LOAD_ARR.push(value)
                    break
                  case 'P_MIN':
                    P_MIN_ARR.push(value)
                    break
                  case 'P_MAX':
                    P_MAX_ARR.push(value)
                    break
                  case 'CM_P_MIN':
                    CM_P_MIN_ARR.push(value)
                    break
                  case 'CM_P_MAX':
                    CM_P_MAX_ARR.push(value)
                    break
                  default:
                    break
                }
              }
            }
          }
        })
        const P_GEN_RGUS = P_GEN_RGU_ARR.reduce((acc, cur) => acc + cur, 0)
        const AVRCHM_LOAD_RGUS = AVRCHM_LOAD_ARR.reduce((acc, cur) => acc + cur, 0)
        const P_MIN_RGUS = P_MIN_ARR.reduce((acc, cur) => acc + cur, 0)
        const P_MAX_RGUS = P_MAX_ARR.reduce((acc, cur) => acc + cur, 0)
        const CM_P_MIN_RGUS = CM_P_MIN_ARR.reduce((acc, cur) => acc + cur, 0)
        const CM_P_MAX_RGUS = CM_P_MAX_ARR.reduce((acc, cur) => acc + cur, 0)

        const IS_VIEW_CM_P_MIN = CM_P_MIN_ARR.length > 0
        const IS_VIEW_CM_P_MAX = CM_P_MAX_ARR.length > 0

        const validObject = {
          is_Valid_P_GEN:
            P_GEN_RGU_ARR.length > 0 ? Number(P_GEN_PLANT.toFixed(3)) !== Number(P_GEN_RGUS.toFixed(3)) : false,
          is_Valid_AVRCHM_LOAD:
            AVRCHM_LOAD_ARR.length > 0
              ? Number(AVRCHM_LOAD_PLANT.toFixed(3)) !== Number(AVRCHM_LOAD_RGUS.toFixed(3))
              : false,
          is_Valid_P_MIN: P_MIN_ARR.length > 0 ? Number(P_MIN_PLANT.toFixed(3)) < Number(P_MIN_RGUS.toFixed(3)) : false,
          is_Valid_P_MAX: P_MAX_ARR.length > 0 ? Number(P_MAX_PLANT.toFixed(3)) > Number(P_MAX_RGUS.toFixed(3)) : false,
          is_Valid_CM_P_MIN: IS_VIEW_CM_P_MIN
            ? Number(CM_P_MIN_PLANT !== undefined ? CM_P_MIN_PLANT.toFixed(3) : 0) <
              Number(CM_P_MIN_RGUS !== undefined ? CM_P_MIN_RGUS.toFixed(3) : 0)
            : false,
          is_Valid_CM_P_MAX: IS_VIEW_CM_P_MAX
            ? Number(CM_P_MAX_PLANT !== undefined ? CM_P_MAX_PLANT.toFixed(3) : 0) >
              Number(CM_P_MAX_RGUS !== undefined ? CM_P_MAX_RGUS.toFixed(3) : 0)
            : false,
        }
        const REGULATED_UNIT = this.plantsData.find((p) => p.plantId === Number(plantId))?.regulatedUnit

        let inputResultProps: IInputResultItemProp | undefined
        this.inputResultStartColumnIndexes.forEach((stationColNum, idx) => {
          if (
            (idx === 0 && el.col < stationColNum) ||
            (idx > 0 && el.col >= this.inputResultStartColumnIndexes[idx - 1] && el.col < stationColNum)
          ) {
            inputResultProps = this.vaultSpreadsheet.inputResultProps[idx]
          }
        })
        const VALIDATE_MAIN = getValidGES(null, keyStation, object, value, inputResultProps)
        const VALIDATE_PLANT_WITH_RGU = additionalValidation(
          type,
          object,
          value,
          keyStation,
          REGULATED_UNIT,
          validObject,
        )

        const isValid = VALIDATE_MAIN === undefined && VALIDATE_PLANT_WITH_RGU === undefined
        const VALIDATE_MESSAGE_MAIN = VALIDATE_MAIN ?? ''
        const VALIDATE_MESSAGE_PLANT_WITH_RGU = VALIDATE_PLANT_WITH_RGU ?? ''
        const comment = VALIDATE_MESSAGE_MAIN + VALIDATE_MESSAGE_PLANT_WITH_RGU
        let editor: Handsontable.GridSettings['editor'] = el.editor
        let manual: boolean | undefined = el.manual
        let fixed: boolean | undefined = el.fixed
        if (
          isAvrchmKey &&
          (FLOOD_MODE_WATCH || plant?.accepted || plant?.viewOnly || this._isLastDay || this._isFinishStage)
        ) {
          editor = false
          if (FLOOD_MODE_WATCH) {
            manual = undefined
            fixed = undefined
          }
        } else if (isAvrchmKey && !FLOOD_MODE_WATCH && !plant?.accepted && !plant?.viewOnly) {
          editor = 'numeric'
        }
        const renderer = getStatusCell(
          el.isMaxConsumptionHour,
          el.isMinConsumptionHour,
          editor,
          el.keyStation,
          el.type,
          isValid,
          manual || fixed,
        )

        return {
          ...el,
          manual,
          fixed,
          editor,
          renderer,
          allowedZones: el.allowedZones,
          comment: isValid ? undefined : getStyledComment(comment),
        }
      })
    }
  }

  changeDataAfterEnableFloodMode = () => {
    let avrchms_plant_ids: string[] = []
    let cursor: number = 0
    this._plantsListForAside
      .filter((el) => el.mixing)
      .forEach((el) => {
        const plant = this.plantsData.find((ps) => ps.plantId === el.plantId)
        if (!plant) return

        const rgus = plant?.rgus
        const delta = 11 * (1 + (rgus?.length ?? 0))
        const rgus_indexs: string[] = []
        const FLOOD_MODE_WATCH = this.floodsLeft.some((id) => id === Number(plant.plantId))
        rgus?.forEach((rgu, index) => {
          rgus_indexs.push(`${3 + index * 3}-P_MIN_RESULT-${rgu.rguId}`)
          if (FLOOD_MODE_WATCH) {
            rgus_indexs.push(`${4 + index * 3}-P_GEN-${rgu.rguId}`)
          }
          rgus_indexs.push(`${5 + index * 3}-P_MAX_RESULT-${rgu.rguId}`)
          rgus_indexs.push(`${3 + rgus.length * 3 + 1 + (1 + index) * 2}-AVRCHM-${rgu.rguId}`)
        })
        cursor = cursor + delta
        avrchms_plant_ids = [
          ...avrchms_plant_ids,
          ...new Array(delta).fill(plant.plantId).map((el, index) => {
            const FLOOD_MODE_WATCH = this.floodsLeft.some((id) => id === Number(el))
            if (index === 0) {
              return `${el}-P_MIN_RESULT`
            }
            if (index === 1 && FLOOD_MODE_WATCH) {
              return `${el}-P_GEN`
            }
            if (index === 2) {
              return `${el}-P_MAX_RESULT`
            }
            if (index === 2 + rgus?.length * 3 + 2) {
              return `${el}-AVRCHM`
            }
            if (rgus.length > 0) {
              const find = rgus_indexs.find((el) => Number(el.split('-')[0]) === index)
              if (find) {
                const [_, key, rguId] = find.split('-')

                return `${el}-${key}-${rguId}`
              }
            }

            return `${el}`
          }),
        ]
      })

    const newEditCellUp = [...this.editCellsUp]
    runInAction(() => {
      this.vaultSpreadsheet.data = this.vaultSpreadsheet.data.map((item, index) => {
        return item.map((el, indexx) => {
          if (avrchms_plant_ids[indexx] === undefined) {
            return el
          }
          const composite_key = avrchms_plant_ids[indexx].split('-')
          const [plantId, key, rguId] = composite_key
          const isEdit = this.idsEditFloodsLeft.some((id) => id === Number(plantId))
          const FLOOD_MODE_WATCH = this.floodsLeft.some((id) => id === Number(plantId))
          const plant = this.plantsData.find((ps) => ps.plantId === Number(plantId))
          const rgus = plant ? plant.rgus : []
          if (composite_key.length > 2 && isEdit) {
            if (key === 'AVRCHM' && FLOOD_MODE_WATCH) {
              newEditCellUp.push(`${index}-${indexx}`)

              return 0
            }
            if (key === 'AVRCHM' && !FLOOD_MODE_WATCH) {
              return el
            }
            if (key === 'P_MIN_RESULT') {
              const keyRgu = rgus.findIndex((el) => el.rguId === Number(rguId))
              const key_P_MAX_RGU = calcCellFromAlphabet(1 + indexx + 2) //+1 for romule
              const avrchm_cell = calcCellFromAlphabet(
                1 + indexx + 2 + (rgus.length - (keyRgu + 1)) * 3 + 2 + (keyRgu + 1) * 2,
              )
              const limit_min_cell = calcCellFromAlphabet(
                1 +
                  indexx +
                  2 +
                  (rgus.length - (keyRgu + 1)) * 3 +
                  2 +
                  (keyRgu + 1) * 2 +
                  (rgus.length - (keyRgu + 1)) * 2 +
                  1 +
                  (keyRgu + 1) * 2,
              )
              if (FLOOD_MODE_WATCH) {
                return `=ROUND(MAX(0,${key_P_MAX_RGU}${index + 1} - 1),3)`
              } else {
                return `=ROUND(MAX(0,${limit_min_cell}${index + 1}+${avrchm_cell}${index + 1}),3)`
              }
            }
            if (key === 'P_GEN' && FLOOD_MODE_WATCH && this.handsontableInstance) {
              const keyRgu = rgus.findIndex((el) => el.rguId === Number(rguId))
              const limit_max_cell = calcCellFromAlphabet(
                1 +
                  indexx +
                  (rgus.length - (keyRgu + 1)) * 3 +
                  2 +
                  (keyRgu + 1) * 2 +
                  (rgus.length - (keyRgu + 1)) * 2 +
                  1 +
                  (keyRgu + 1) * 2 +
                  2,
              )
              newEditCellUp.push(`${index}-${indexx}`)

              return calculateSpreadsheetValueByFormula(
                this.handsontableInstance,
                `=ROUND(${limit_max_cell}${index + 1},3)`,
              )
            }
            if (key === 'P_MAX_RESULT') {
              const keyRgu = rgus.findIndex((el) => el.rguId === Number(rguId))
              const avrchm_cell = calcCellFromAlphabet(
                1 + indexx + (rgus.length - (keyRgu + 1)) * 3 + 2 + (keyRgu + 1) * 2,
              )
              const limit_max_cell = calcCellFromAlphabet(
                1 +
                  indexx +
                  (rgus.length - (keyRgu + 1)) * 3 +
                  2 +
                  (keyRgu + 1) * 2 +
                  (rgus.length - (keyRgu + 1)) * 2 +
                  1 +
                  (keyRgu + 1) * 2 +
                  1,
              )

              return `=ROUND(${limit_max_cell}${index + 1}-${avrchm_cell}${index + 1},3)`
            }
          } else if (composite_key.length > 1 && isEdit) {
            const plant = this.plantsData.find((ps) => ps.plantId === Number(plantId))
            const allowedZones = plant?.allowedZones[index]?.zones ?? []
            const REGULATED_UNIT = plant?.regulatedUnit ?? 'PLANT'
            const rgus = plant?.rgus ?? []
            if (key === 'P_MIN_RESULT') {
              if (FLOOD_MODE_WATCH) {
                if (REGULATED_UNIT === 'PLANT') {
                  const key_P_MAX = indexx + 3

                  return `=ROUND(MAX(0,${calcCellFromAlphabet(key_P_MAX)}${index + 1} - 1),3)`
                } else {
                  const rgusSum = rgus.map((_, key) => {
                    return `${calcCellFromAlphabet(indexx + 1 + (key + 1) * 3)}${index + 1}`
                  })
                  const rgusForPminString = rgusSum.join('+')

                  return `=ROUND(MAX(0,${rgusForPminString}),3)`
                }
              } else {
                const limit_min_cell = calcCellFromAlphabet(indexx + 3 * (rgus.length + 1) + 3 + 2 * rgus.length)
                const avrchm_cell = calcCellFromAlphabet(indexx + 3 * (rgus.length + 1) + 2)

                return makePMinFormula(
                  allowedZones,
                  `ROUND(MAX(0,${limit_min_cell}${index + 1}+${avrchm_cell}${index + 1}),3)`,
                )
              }
            }
            if (key === 'P_GEN' && FLOOD_MODE_WATCH && this.handsontableInstance) {
              if (REGULATED_UNIT === 'PLANT') {
                const limit_max_cell = calcCellFromAlphabet(indexx + 1 + 3 * (rgus.length + 1) + 2 + 2 * rgus.length)
                newEditCellUp.push(`${index}-${indexx}`)

                return calculateSpreadsheetValueByFormula(
                  this.handsontableInstance,
                  `=ROUND(MAX(0,${limit_max_cell}${index + 1}),3)`,
                )
              } else if (this.handsontableInstance) {
                const rgusSum = rgus.map((_, key) => {
                  return `ROUND(MAX(0,${calcCellFromAlphabet(indexx - 1 + (rgus.length + 1) * 3 + 2 + (rgus.length + 1) * 2 + 2 + key * 2)}${index + 1}),3)`
                })
                // сумма Итог.Огр (макс)
                const rgusForPGenString = rgusSum.join('+')
                newEditCellUp.push(`${index}-${indexx}`)

                return calculateSpreadsheetValueByFormula(
                  this.handsontableInstance,
                  `=ROUND(MAX(0,${rgusForPGenString}),3)`,
                )
              }
            }
            if (key === 'P_MAX_RESULT') {
              const limit_max_cell = calcCellFromAlphabet(indexx + 3 * (rgus.length + 1) + 2 + 2 * rgus.length)
              const avrchm_cell = calcCellFromAlphabet(indexx + 3 * (rgus.length + 1))
              if (FLOOD_MODE_WATCH) {
                if (REGULATED_UNIT === 'PLANT') {
                  return `=ROUND(MAX(0,${limit_max_cell}${index + 1}-${avrchm_cell}${index + 1}),3)`
                } else {
                  const rgusSum = rgus.map((_, key) => {
                    return `${calcCellFromAlphabet(indexx + 1 + (key + 1) * 3)}${index + 1}`
                  })
                  const rgusForPminString = rgusSum.join('+')

                  return `=ROUND(MAX(0,${rgusForPminString}),3)`
                }
              } else {
                return makePMaxFormula(
                  allowedZones,
                  `ROUND(MAX(0,${limit_max_cell}${index + 1}-${avrchm_cell}${index + 1}),3)`,
                )
              }
            }
            if (key === 'AVRCHM') {
              newEditCellUp.push(`${index}-${indexx}`)

              return 0
            }
          } else {
            return el
          }

          return undefined
        })
      })
      this.vaultSpreadsheet.column = this.vaultSpreadsheet.column.map((el, index) => {
        if (avrchms_plant_ids[index] === undefined) {
          return el
        }
        const [plantId, key] = avrchms_plant_ids[index].split('-')
        const FLOOD_MODE_WATCH = this.floodsLeft.some((id) => id === Number(plantId))
        if (key === 'AVRCHM' && FLOOD_MODE_WATCH) {
          return { editor: false, readOnly: false }
        }
        if (key === 'AVRCHM' && !FLOOD_MODE_WATCH) {
          return { editor: 'numeric', readOnly: false }
        }

        return el
      })
      this.editCellsUp = Array.from(new Set(newEditCellUp))
      setTimeout(() => {
        this.validate(this.vaultSpreadsheet.data, null)
      }, 0)
    })
  }

  //Получение статуса загрузки данных
  async getStatusDataVault(calcDate: string, planingStage: string) {
    try {
      const { status } = await api.calculationsManager.getGeneralStatusVault(calcDate, planingStage)
      runInAction(() => {
        this.statusVaultUpdateData = status
      })
    } catch (e) {
      console.log(e)
    }
  }

  changeOpenAndCloseModalUpdateVault(value: boolean) {
    this.isModalUpdateVault = value
  }

  initActionsLeft = (data?: Plant[]) => {
    const plantsList = data ? data : this._plantsListForAside
    const actionsLeft = JSON.parse(localStorage.getItem('actionsLeft') as string)
    if (!actionsLeft) {
      this.actionsLeft = plantsList
        .filter((el) => el.type === 'GES')
        .filter((el) => el.mixing)
        .filter((el) => !el.viewOnly)
        .map((el) => el.plantId)
    } else {
      this.actionsLeft = actionsLeft
    }

    const headers = plantsList
      .filter((el) => el.mixing)
      .flatMap((el) => {
        const plant = this.plantsData.find((ps) => ps.plantId === el.plantId)
        const plantRgus = plant ? plant.rgus : []

        return new Array(11 + plantRgus.length * 11).fill(() => null).map(() => el.accepted)
      })
      .map((el, index) => (el ? index : null))
      .filter((el) => el !== null)

    this.vaultSpreadsheet.customHeaders = this.vaultSpreadsheet.customHeaders.map((el) => {
      const accepted = headers.some((headerColIdx) => headerColIdx === el.col)
      const className = accepted ? 'acceptedBgColor' : ''
      const finished = this._planningStage !== el.code
      if (!el.code) {
        return el
      }

      return {
        ...el,
        className,
        stageClassName: getColorStage(el.code, finished),
        thText: PlanningStageRussia[el.code],
      }
    })
  }

  changeActionsLeft(card: { plantId: number }) {
    const find = this.actionsLeft.find((el) => el === card.plantId)
    if (find) {
      this.actionsLeft = this.actionsLeft.filter((el) => el !== card.plantId)
    } else {
      this.actionsLeft = [...this.actionsLeft, card.plantId]
    }
    localStorage.setItem('actionsLeft', JSON.stringify(this.actionsLeft))
  }

  changeAllActionsLeft = (active: boolean) => {
    this.actionsLeft = this._plantsListForAside
      .filter((el) => active && el.mixing && !el.viewOnly)
      .map((el) => el.plantId)
    localStorage.setItem('actionsLeft', JSON.stringify(this.actionsLeft))
  }

  changeFloodsLeft(card: { plantId: number }) {
    runInAction(() => {
      const find = this.floodsLeft.find((el) => el === card.plantId)
      if (find) {
        this.floodsLeft = this.floodsLeft.filter((el) => el !== card.plantId)
        this.plantsData = this.plantsData.map((plant) => {
          if (plant.plantId === card.plantId) {
            const allowedZones = plant.allowedZones.map((zone) => {
              return { ...zone, zones: [] }
            })
            const rgus = plant.rgus.map((rgu) => {
              const allowedZones = plant.allowedZones.map((zone) => {
                return { ...zone, zones: [] }
              })

              return { ...rgu, allowedZones }
            })

            return { ...plant, allowedZones, rgus }
          }

          return plant
        })
        this.vaultData = this.vaultData.map((row) => {
          const key = `${card.plantId}-allowedZones`

          return { ...row, [key]: [] }
        })
        this._avrchmStore.changeFloodModeForAvrchmByPlantId(card.plantId, false)
      } else {
        this.vaultSpreadsheet.cell = this.vaultSpreadsheet.cell.map((c) => {
          if (c.idStation === card.plantId) {
            return {
              ...c,
              allowedZones: [],
            }
          }

          return c
        })
        this.floodsLeft = [...this.floodsLeft, card.plantId]
        this._avrchmStore.changeFloodModeForAvrchmByPlantId(card.plantId, true)
      }
      const idsEditFloodsLeft = [...this.idsEditFloodsLeft, card.plantId]
      this.idsEditFloodsLeft = [...new Set(idsEditFloodsLeft)]
      this.changeDataAfterEnableFloodMode()
      this.recalculateInputValues()
    })
  }

  downloadAvrchmTes = async () => {
    try {
      this.isLoadingAvrchmTes = true
      await api.calculationsManager.downloadAvrchmTes({
        targetDate: format(this._date, 'yyyy-MM-dd'),
        planingStage: this._planningStage,
      })
    } catch (error) {
      this.isLoadingAvrchmTes = false
      console.error('Произошла ошибка при загрузке АВРЧМ Модес', error)
    }
  }

  changeViewModalAcceptedVault(status: boolean) {
    this.isViewModalAcceptedVault = status
  }

  changePlantsAcceptVaultSelected(ids: number[]) {
    this.plantsAcceptVaultSelected = ids
  }

  checkCalcPossibilityVault = async () => {
    try {
      const res = await api.calculationsManager.getCalcPossibilityVault(
        format(this._date, 'yyyy-MM-dd'),
        this._planningStage,
      )
      runInAction(() => {
        this.rootStore.calculationsPageStore.calcPossibility = res
      })
    } catch (e) {
      console.log(e)
    }
  }

  updateModalUpdateVaultByIdAndKey(
    plantId: number,
    type: string,
    status: TStatusUpdateModalVault,
    userFio?: string,
    warnings?: string[],
    updatedDate?: string,
  ) {
    try {
      this.isLoadingVaultUpdateData = true
      this.vaultUpdateData = this.vaultUpdateData.map((item) => {
        if (item.plantId === plantId) {
          return {
            ...item,
            [type]: {
              status,
              warnings,
              userFio,
              updatedDate,
            },
          }
        }

        return item
      })
    } catch (e) {
      console.log(e)
    } finally {
      setTimeout(() => {
        this.isLoadingVaultUpdateData = false
      }, TIME_LOADER)
    }
  }

  async initModalUpdateVault() {
    try {
      this.isLoadingVaultUpdateData = true
      const vaultUpdateData = await api.calculationsManager.getDataUpdateVault({
        calcDate: format(this._date, 'yyyy-MM-dd'),
        planingStage: this._planningStage,
      })
      let res: IVaultUpdateData[] = []
      vaultUpdateData.forEach((el) => {
        const find = res.find((item) => item.plantId === el.plantId)
        const warnings = el?.warnings
        const code = el.type.code
        const status = el.status === 'DONE' && warnings && warnings?.length > 0 ? 'WARNING' : el.status
        const updatedDate = el?.updatedDate
        const userFio = el?.userFio
        if (find) {
          res = res.map((item) => {
            if (item.plantId === el.plantId) {
              return {
                ...item,
                tabId: item.plantId,
                plantId: item.plantId,
                plantName: item.plantName,
                [code]: { status, warnings, updatedDate, userFio },
              }
            }

            return item
          })
        } else {
          res.push({
            tabId: el.plantId,
            plantId: el.plantId,
            plantName: el.plantName,
            [code]: { status, warnings, updatedDate, userFio },
          })
        }
      })
      this.vaultUpdateData = res
    } catch (e) {
      console.log(e)
    } finally {
      setTimeout(() => {
        this.isLoadingVaultUpdateData = false
      }, TIME_LOADER)
    }
  }

  changeStatusDataVault(status: TStatusUpdateModalVault) {
    runInAction(() => {
      this.statusVaultUpdateData = status
    })
  }

  checkAllowedZones = (allowedZones: IAllowedZone[], value: number) => {
    return allowedZones?.length > 0 ? allowedZones?.some((el) => value >= el.bottomLine && value <= el.topLine) : false
  }

  debouncedInitLoadDataVault: IVaultStore['debouncedInitLoadDataVault'] = () => {}

  initLoadDataVault: IVaultStore['initLoadDataVault'] = async (shouldSaveRequestStatus?: boolean) => {
    if (shouldSaveRequestStatus) {
      this.setVaultLoadDataStatus('IN_PROCESS')
    }
    const prepareDate = format(this._date, 'yyyy-MM-dd')
    const plantIds = this._plantsListForAside.filter((el) => el.mixing).map((el) => el.plantId)
    try {
      await this.getStatusDataVault(prepareDate, this._planningStage)
      const { plants } = await api.calculationsManager.getDataVault(prepareDate, this._planningStage, plantIds)
      const plantOptimized: number[] = []
      const rows = new Array(24).fill({}).map((_, index: number) => {
        const hour = index + 1
        const res: { [key: string]: string | number | undefined | null | IAllowedZone[] } = {}
        plants.forEach((el) => {
          const FLOOD_MODE_WATCH = el?.inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false
          const maxConsumptionHour = el.maxConsumptionHour ?? -1
          const minConsumptionHour = el.minConsumptionHour ?? -1
          const allowedZones =
            el?.allowedZones?.length > 0 ? (el.allowedZones.find((zone) => zone.hour === index + 1)?.zones ?? []) : []
          if (el.plantOptimized) {
            plantOptimized.push(el.plantId)
          }
          const cells = el.rows.find((item) => item.hour === hour)?.cells ?? []
          const P_GEN = cells.find((item) => item.column === 'P_GEN')
          const AVRCHM_LOAD = cells.find((item) => item.column === 'AVRCHM_LOAD')
          const AVRCHM_UNLOAD = cells.find((item) => item.column === 'AVRCHM_UNLOAD')
          const CM_P_MIN = cells.find((item) => item.column === 'CM_P_MIN')
          const CM_P_MAX = cells.find((item) => item.column === 'CM_P_MAX')
          const MODES_P_MIN = cells.find((item) => item.column === 'MODES_P_MIN')
          const MODES_P_MAX = cells.find((item) => item.column === 'MODES_P_MAX')
          const CONSUMPT = cells.find((item) => item.column === 'CONSUMPT')
          const P_SOURCE = cells.find((item) => item.column === 'P_SOURCE')
          const RESULT_MIN = cells.find((item) => item.column === 'RESULT_MIN')
          const RESULT_MAX = cells.find((item) => item.column === 'RESULT_MAX')

          const rgus = el.rgus
          const RgusPmin = rgus.reduce((total, rgu) => {
            const cells = rgu?.rows[index].cells
            const RESULT_MIN = cells.find((cell) => cell.column === 'RESULT_MIN')?.value ?? 0
            const AVRCHM_LOAD = cells.find((cell) => cell.column === 'AVRCHM_LOAD')?.value ?? 0

            return total + RESULT_MIN + AVRCHM_LOAD
          }, 0)
          const RgusPmax = rgus.reduce((total, rgu) => {
            const cells = rgu?.rows[index].cells
            const RESULT_MAX = cells.find((cell) => cell.column === 'RESULT_MAX')?.value ?? 0
            const AVRCHM_LOAD = cells.find((cell) => cell.column === 'AVRCHM_LOAD')?.value ?? 0

            return total + RESULT_MAX - AVRCHM_LOAD
          }, 0)
          const getDefaultPmin = () => {
            if (CM_P_MIN && MODES_P_MIN) {
              return Math.max(CM_P_MIN.value ?? 0, MODES_P_MIN.value ?? 0)
            }
            if (!CM_P_MIN && !MODES_P_MIN) {
              return undefined
            }
            if (!CM_P_MIN) {
              return MODES_P_MIN?.value ?? 0
            }
            if (!MODES_P_MIN) {
              return CM_P_MIN?.value ?? 0
            }
          }
          const getPmin = () => {
            if (el.regulatedUnit === 'PLANT') {
              return getDefaultPmin()
            } else {
              if (FLOOD_MODE_WATCH) {
                return RgusPmin
              } else {
                return getDefaultPmin()
              }
            }
          }

          const getDefaultPmax = () => {
            if (CM_P_MAX && MODES_P_MAX) {
              return Math.min(CM_P_MAX.value ?? 0, MODES_P_MAX.value ?? 0)
            }
            if (!CM_P_MAX && !MODES_P_MAX) {
              return undefined
            }
            if (!CM_P_MAX) {
              return MODES_P_MAX?.value ?? 0
            }
            if (!MODES_P_MAX) {
              return CM_P_MAX?.value ?? 0
            }
          }
          const getPmax = () => {
            if (el.regulatedUnit === 'PLANT') {
              return getDefaultPmax()
            } else {
              if (FLOOD_MODE_WATCH) {
                return RgusPmax
              } else {
                return getDefaultPmax()
              }
            }
          }

          const pMin = getPmin() ?? 0
          const pMax = getPmax() ?? 0
          const AVRCHM_LOAD_VALUE = AVRCHM_LOAD?.value ?? 0

          const zoneMin = Math.min(...allowedZones.map((el) => Number(el.bottomLine))) ?? 0
          const zoneMax = Math.max(...allowedZones.map((el) => Number(el.topLine))) ?? 0

          const isAllowedZoneMin = this.checkAllowedZones(allowedZones, pMin + AVRCHM_LOAD_VALUE)
          const isAllowedZoneMax = this.checkAllowedZones(allowedZones, pMax - AVRCHM_LOAD_VALUE)

          let P_MIN_RESULT: number | null =
            isAllowedZoneMin || allowedZones.length === 0 ? pMin + AVRCHM_LOAD_VALUE : zoneMin
          if (el.regulatedUnit === 'PLANT' && RESULT_MIN) {
            P_MIN_RESULT = RESULT_MIN?.value
          }
          let P_MAX_RESULT: number | null = null
          if (pMax - AVRCHM_LOAD_VALUE < 0) {
            P_MAX_RESULT = 0
          } else if (isAllowedZoneMax || allowedZones.length === 0) {
            P_MAX_RESULT = pMax - AVRCHM_LOAD_VALUE
          } else {
            P_MAX_RESULT = zoneMax
          }
          if (el.regulatedUnit === 'PLANT' && RESULT_MAX) {
            P_MAX_RESULT = RESULT_MAX?.value
          }
          const RESERVES_MAX = cells.find((cell) => cell.column === 'RESERVES_MAX')?.value ?? undefined
          res[`${el.plantId}-P_GEN`] = P_GEN?.value ?? ''
          res[`${el.plantId}-AVRCHM_LOAD`] = AVRCHM_LOAD?.value ?? 0
          res[`${el.plantId}-AVRCHM_UNLOAD`] = AVRCHM_UNLOAD?.value ?? 0
          res[`${el.plantId}-P_MIN`] = getDefaultPmin() ?? 0
          res[`${el.plantId}-P_MAX`] = getDefaultPmax() ?? 0
          res[`${el.plantId}-CM_P_MIN`] = CM_P_MIN ? CM_P_MIN?.value : undefined
          res[`${el.plantId}-CM_P_MAX`] = CM_P_MAX ? CM_P_MAX?.value : undefined
          res[`${el.plantId}-MODES_P_MIN`] = MODES_P_MIN ? MODES_P_MIN?.value : undefined
          res[`${el.plantId}-MODES_P_MAX`] = MODES_P_MAX ? MODES_P_MAX?.value : undefined
          res[`${el.plantId}-CONSUMPT`] = CONSUMPT?.value ?? 0
          res[`${el.plantId}-P_SOURCE`] = P_SOURCE?.value ?? 0
          res[`${el.plantId}-P_MIN_RESULT`] = P_MIN_RESULT
          res[`${el.plantId}-P_MAX_RESULT`] = P_MAX_RESULT
          res[`${el.plantId}-RESERVES_MAX`] = RESERVES_MAX

          res[`${el.plantId}-allowedZones`] = allowedZones
          res[`${el.plantId}-maxConsumptionHour`] = maxConsumptionHour
          res[`${el.plantId}-minConsumptionHour`] = minConsumptionHour
        })

        return { tabId: generateUUID(), hour: String(hour), ...res }
      })
      let plantOptimizeds: string[] = []
      prepareUniqueArr(plantOptimized).forEach((el) => {
        plantOptimizeds = [
          ...plantOptimizeds,
          `${el}-P_GEN`,
          `${el}-AVRCHM_LOAD`,
          `${el}-AVRCHM_UNLOAD`,
          `${el}-P_MIN`,
          `${el}-P_MAX`,
          `${el}-CM_P_MIN`,
          `${el}-CM_P_MAX`,
          `${el}-MODES_P_MIN`,
          `${el}-MODES_P_MAX`,
          `${el}-CONSUMPT`,
          `${el}-P_SOURCE`,
          `${el}-P_MIN_RESULT`,
          `${el}-P_MAX_RESULT`,
          `${el}-RESERVES_MAX`,
        ]
      })
      runInAction(() => {
        this.vaultData = rows
        this.vaultDataOriginal = rows
        this.inputValues = [] // Костыль для сброса значений в строке Э при смене даты/этапа
        this.vaultSpreadsheet.inputResultProps = [] // Костыль для сброса значений в строке Э при смене даты/этапа
        this.plantsData = plants
        this.plantsDataOriginal = plants
        this.floodsLeft = []
        this._floodsLeftOriginal = []
        this.shouldUpdateVault = false
        this.idsEditFloodsLeft = []
        this.editCellsUp = []
        plants.forEach((plant) => {
          const FLOOD_MODE_WATCH = plant?.inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false
          if (FLOOD_MODE_WATCH) {
            this.floodsLeft = [...this.floodsLeft, plant.plantId]
            this._floodsLeftOriginal = [...this._floodsLeftOriginal, plant.plantId]
          }
        })
        this.recalculateInputValues()
      })
      this.convertVaultDataToSpreadsheetProps({
        vaultData: rows,
        plantsData: plants,
      })
      await this.checkCalcPossibilityVault()
      this.setVaultLoadDataStatus('DONE')
    } catch (error) {
      this.vaultData = []
      this.vaultDataOriginal = []
      console.error(error)
    }
  }

  resetData = () => {
    runInAction(() => {
      this.vaultSpreadsheet.colNumberPerStation = []
      this.inputValues = this._originalInputValues
      this.vaultSpreadsheet = klona(this.originalVaultSpreadsheet)
      this._avrchmStore.resetAvrchm()
      this.editCellsUp = []
      this.floodsLeft = [...this._floodsLeftOriginal]
      this.idsEditFloodsLeft = []
      this.selectedCellsBeforeFix = []
      this.inputValuesChanged = false
    })
    this.recalculateInputValues()
  }

  // Функция для маппинга старых названий колонок в новые
  private mapLegacyColumnName = (keyStation: string): CalculationColumn => {
    const columnMapping: Record<string, CalculationColumn> = {
      P_MIN: CalculationColumn.LIMIT_MIN,
      P_MAX: CalculationColumn.LIMIT_MAX,
      P_MIN_RESULT: CalculationColumn.RESULT_MIN,
      P_MAX_RESULT: CalculationColumn.RESULT_MAX,
    }

    return (columnMapping[keyStation] as CalculationColumn) || (keyStation as CalculationColumn)
  }

  save = async () => {
    this.rootStore.calculationsPageStore.setSyncStatus('SAVE', TaskStatus.IN_PROCESS)
    const rguValues: ISaveDataVaultInput['rguValues'] = []
    const calculationValues: ISaveDataVaultInput['calculationValues'] = []
    const inputValues: ISaveDataVaultInput['inputValues'] = []

    // Подготавливаем данные по станциям, которые были изменены на Своде
    this.editCellsUp.forEach((editCell) => {
      const [row, col] = editCell.split('-')
      if (Number(row) > 23) return
      const cellIdx = Number(row) * this.vaultSpreadsheet.column.length + Number(col)
      const cell = this.vaultSpreadsheet.cell[cellIdx]
      const value = this.vaultSpreadsheet.data[Number(row)][Number(col)]
      const formattedValue = value !== undefined && value !== null && value !== '' ? Number(value) : null

      const newCalculationValue: ICalculationValue = {
        column: this.mapLegacyColumnName(cell.keyStation),
        hour: cell.row + 1,
        value: formattedValue,
        manual: formattedValue === null ? undefined : cell.manual,
        fixed: formattedValue === null ? undefined : cell.fixed,
      }

      if (cell.idRGU !== null) {
        const [_, column] = cell.keyStation.split('-')
        const rguIdx = rguValues.findIndex((rgu) => rgu.rguId === cell.idRGU)
        const newRguValues = [{ ...newCalculationValue, column: this.mapLegacyColumnName(column) }]
        // АВРЧМ выгрузки нужно обязательно сохранять для корректной работы расчетов
        // Позже этот параметр появится в таблице и его можно будет удалить
        if (newRguValues[0].column === CalculationColumn.AVRCHM_LOAD) {
          newRguValues.push({ ...newCalculationValue, column: CalculationColumn.AVRCHM_UNLOAD })
        }
        if (rguIdx !== -1) {
          rguValues[rguIdx].values.push(...newRguValues)
        } else {
          rguValues.push({
            plantId: cell.idStation,
            rguId: cell.idRGU,
            values: newRguValues,
          })
        }
      } else {
        const calculationValueIdx = calculationValues.findIndex((plant) => plant.plantId === cell.idStation)
        const newCalculationValues = [newCalculationValue]
        if (newCalculationValues[0].column === CalculationColumn.AVRCHM_LOAD) {
          newCalculationValues.push({ ...newCalculationValue, column: CalculationColumn.AVRCHM_UNLOAD })
        }
        if (calculationValueIdx !== -1) {
          calculationValues[calculationValueIdx].values.push(...newCalculationValues)
        } else {
          calculationValues.push({
            plantId: cell.idStation,
            values: newCalculationValues,
          })
        }
      }
    })

    // Подготавливаем данные по станциям, которые были изменены в таблице АВРЧМ и отсутствуют на Своде
    const alreadyAddedPlantsIds = calculationValues.map((calculationValue) => calculationValue.plantId)
    this._avrchmStore.changedAvrchmCells.forEach((cellIdx) => {
      const cell = this._avrchmStore.avrchmSpreadsheet.cell[cellIdx]
      if (cell.plantId === undefined || alreadyAddedPlantsIds.includes(cell.plantId)) {
        return
      }
      const formattedValue =
        cell.value !== undefined && cell.value !== null && cell.value !== '' ? Number(cell.value) : null

      const newCalculationValue: ICalculationValue = {
        column: CalculationColumn.AVRCHM_LOAD,
        hour: cell.row + 1,
        value: formattedValue,
        manual: formattedValue === null ? undefined : cell.manual,
        fixed: formattedValue === null ? undefined : cell.fixed,
      }

      const calculationValueIdx = calculationValues.findIndex((plant) => plant.plantId === cell.plantId)
      const newCalculationValues = [
        newCalculationValue,
        { ...newCalculationValue, column: CalculationColumn.AVRCHM_UNLOAD },
      ]
      if (calculationValueIdx !== -1) {
        calculationValues[calculationValueIdx].values.push(...newCalculationValues)
      } else {
        calculationValues.push({
          plantId: cell.plantId,
          values: newCalculationValues,
        })
      }
    })

    // Подготавливаем данные по измененным значениям Эмин, Эплан, Эмакс
    this.inputValues.forEach((item) => {
      const plantData = this.plantsData.find((plant) => plant.plantId === item.plantId)
      const plant = this._plantsListForAside.find((el) => el.plantId === item.plantId)
      if (plantData && !plantData.accepted && !plant?.viewOnly) {
        const values = plantData.inputValues
        const is_P_GEN_TARGET = Object.keys(item).some((el) => el === 'P_GEN_TARGET')
        const is_W_MIN =
          Object.keys(item).some((el) => el === 'W_MIN') && Object.keys(values).some((el) => el === 'W_MIN')
        const is_W_MAX = Object.keys(item).some((el) => el === 'W_MAX')
        const is_FLOOD_MODE_WATCH = Object.keys(item).some((el) => el === 'FLOOD_MODE_WATCH')
        const isFloodModeWatchActive = this.floodsLeft.some((id) => id === item.plantId)
        if (isFloodModeWatchActive) {
          if (is_P_GEN_TARGET) {
            values.P_GEN_TARGET!.value = Number(item.P_GEN_TARGET) * 1000
          }
          if (is_W_MIN) {
            values.W_MIN.value = Number(item.W_MIN) * 1000
          }
          if (is_W_MAX) {
            values.W_MAX!.value = Number(item.W_MAX) * 1000
          }
        } else {
          if (is_P_GEN_TARGET) {
            const isNumber = !Number.isNaN(parseFloat(String(item.P_GEN_TARGET)))
            values.P_GEN_TARGET!.value = isNumber ? Number(item.P_GEN_TARGET) * 1000 : undefined
          }
          if (is_W_MIN) {
            const isNumber = !Number.isNaN(parseFloat(String(item.W_MIN)))
            if (isNumber) {
              values.W_MIN.value = Number(item.W_MIN) * 1000 <= 0 ? 0 : Number(item.W_MIN) * 1000
            } else {
              values.W_MIN.value = undefined
            }
          }
          if (is_W_MAX) {
            const isNumber = !Number.isNaN(parseFloat(String(item.W_MAX)))
            if (isNumber) {
              values.W_MAX!.value = Number(item.W_MAX) * 1000 <= 0 ? 0 : Number(item.W_MAX) * 1000
            } else {
              values.W_MAX!.value = undefined
            }
          }
        }
        if (is_FLOOD_MODE_WATCH) {
          values.FLOOD_MODE_WATCH!.value.turnedOn = isFloodModeWatchActive
        }
        inputValues.push({
          plantId: item.plantId,
          values,
        })
      }
    })

    const payload: ISaveDataVaultInput = {
      targetDate: format(this._date, 'yyyy-MM-dd'),
      planingStage: this._planningStage,
      rguValues,
      calculationValues,
      inputValues,
    }

    try {
      const { warnings } = await api.calculationsManager.saveDataVault(payload)
      if (warnings.length > 0) {
        this.rootStore.notificationStore.addNotification({
          title: `Ошибка при сохранении`,
          description: warnings.join(' ;'),
          type: 'error',
          multiError: true,
        })
      }
      this.rootStore.notificationStore.addNotification({
        title: 'Сохранение',
        description: 'Данные сохранены',
        type: 'success',
      })
      await runInAction(async () => {
        this._floodsLeftOriginal = [...this.floodsLeft]
        this.idsEditFloodsLeft = []
        this.editCellsUp = []
        this._avrchmStore.selectedCellsBeforeFix = []
        await this.initLoadDataVault()
        this.rootStore.calculationsPageStore.setSyncStatus('SAVE', TaskStatus.DONE)
        await this._avrchmStore.initAvrchm(true)
        this.inputValuesChanged = false
      })
    } catch (e) {
      console.log('error', e)
    }
  }

  async actionsVault(type: MessagesWarnings, plants: Plant[], method?: 'DIVING_BELL' | 'MAXIMUM') {
    const plantIds: number[] = []
    this.actionsLeft.forEach((id) => {
      const plant = plants.find((plant) => plant.plantId === id) ?? null
      if (!plant?.accepted && plant?.mixing) {
        plantIds.push(id)
      }
    })
    const params = {
      targetDate: format(this._date, 'yyyy-MM-dd'),
      planingStage: this._planningStage,
      plantIds,
      method,
    }
    try {
      runInAction(() => {
        this.statusVaultUpdateData = 'IN_PROCESS'
      })
      switch (type) {
        case MessagesWarnings.LOAD_ALL:
          this.rootStore.notificationStore.addNotification({
            title: 'Загрузка по станциям запущена',
            description: 'Загрузка всех исходных данных началась',
            type: 'success',
          })
          await api.calculationsManager.calcLoadAllVault(params, null)
          break
        case MessagesWarnings.LOAD_ISP:
          this.rootStore.notificationStore.addNotification({
            title: 'Загрузка по станциям запущена',
            description: 'Загрузка графика потребления началась',
            type: 'success',
          })
          await api.calculationsManager.calcLoadAllVault(params, CalculationTaskType.LOAD_CONSUMPTION)
          break
        case MessagesWarnings.LOAD_MODES:
          this.rootStore.notificationStore.addNotification({
            title: 'Загрузка по станциям запущена',
            description: 'Загрузка данных из Модеса началась',
            type: 'success',
          })
          await api.calculationsManager.calcLoadAllVault(params, CalculationTaskType.LOAD_PLANT_DATA)
          await api.calculationsManager.calcLoadAllVault(params, CalculationTaskType.LOAD_GENERATOR_ALLOWED_ZONES)
          break
        case MessagesWarnings.LOAD_CM:
          this.rootStore.notificationStore.addNotification({
            title: 'Загрузка по станциям запущена',
            description: 'Загрузка данных из РМ началась',
            type: 'success',
          })
          await api.calculationsManager.calcLoadAllVault(params, CalculationTaskType.LOAD_CM_DATA)
          break
        case MessagesWarnings.DO_OPTIMIZATION:
          this.rootStore.notificationStore.addNotification({
            title: 'Расчёт по станциям запущен',
            description: 'Оптимизация началась',
            type: 'success',
          })
          await api.calculationsManager.calcOptimizationVault(params)
          break
        case MessagesWarnings.CALCULATE_ALLOWED_ZONES:
          this.rootStore.notificationStore.addNotification({
            title: 'Расчёт по станциям запущен',
            description: 'Расчёт допустимых зон начался',
            type: 'success',
          })
          await api.calculationsManager.calcAllowedZonesVault(params)
          break
        case MessagesWarnings.CALCULATE_GENERATION:
          this.rootStore.notificationStore.addNotification({
            title: 'Расчёт по станциям запущен',
            description: 'Расчёт плановой генерации начался',
            type: 'success',
          })
          await api.calculationsManager.calcGenerationVault(params)
          break
        case MessagesWarnings.CALCULATE_GENERATION_MAXIMUM:
          this.rootStore.notificationStore.addNotification({
            title: 'Расчёт по станциям запущен',
            description: 'Расчёт максимальной генерации начался',
            type: 'success',
          })
          await api.calculationsManager.calcGenerationVault(params)
          break
        case MessagesWarnings.ENTERING_ALLOWED_ZONES:
          this.rootStore.notificationStore.addNotification({
            title: 'Расчёт по станциям запущен',
            description: 'Расчёт ввода в допустимые зоны относительно графика потребления начался',
            type: 'success',
          })
          await api.calculationsManager.enteringAllowedZones(params, AllowedZonedMode.CONSUMPTION_SCHEDULE_CHANGE)
          break
        case MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS:
          this.rootStore.notificationStore.addNotification({
            title: 'Расчёт по станциям запущен',
            description: 'Расчёт ввода в допустимые зоны в сторону ближайшей границы начался',
            type: 'success',
          })
          await api.calculationsManager.enteringAllowedZones(params, AllowedZonedMode.ROUND_TO_BOUND)
          break
        case MessagesWarnings.BALANCE_RGU:
          this.rootStore.notificationStore.addNotification({
            title: 'Расчёт по станциям запущен',
            description: 'Распределение нагрузки по РГЕ началось',
            type: 'success',
          })
          await api.calculationsManager.rguGenerationVault(params)
          break
        default:
          break
      }
    } catch (e) {
      this.changeStatusDataVault('FAILED')
      console.log(e)
    }
  }

  async acceptVault(forced: boolean, plants: ICalculationsPageStore['plantsListForAside']) {
    try {
      let plantIds: number[] = []
      if (forced) {
        plantIds = this.plantsAcceptVaultSelected
      } else {
        this.actionsLeft.forEach((id) => {
          const plant = plants.find((plant) => plant.plantId === id) ?? null
          if (!plant?.accepted && plant?.mixing) {
            plantIds.push(id)
          }
        })
      }
      const plantsAcceptVault = await api.calculationsManager.acceptVault(
        {
          targetDate: format(this._date, 'yyyy-MM-dd'),
          planingStage: this._planningStage,
          plantIds,
        },
        forced,
      )
      if (this._selectLeftMenu !== 0) return
      this.plantsAcceptVault = plantsAcceptVault.sort((a, b) => {
        return b.success === a.success ? 0 : b.success ? 1 : -1
      })
      plantsAcceptVault.forEach((acceptedPlant) => {
        const foundPlantIdx = this.plantsData.findIndex(
          (plant) => plant.plantId === acceptedPlant.plantId && acceptedPlant.success,
        )
        if (foundPlantIdx !== -1 && !!this.plantsData[foundPlantIdx]) {
          this.plantsData[foundPlantIdx].accepted = true
        }

        const foundAsidePlantIdx = this._plantsListForAside.findIndex(
          (plant) => plant.plantId === acceptedPlant.plantId && acceptedPlant.success,
        )
        if (foundAsidePlantIdx !== -1 && !!this._plantsListForAside[foundAsidePlantIdx]) {
          this._plantsListForAside[foundAsidePlantIdx].accepted = true
        }
      })
      this.plantsData = [...this.plantsData]
      this.plantsDataOriginal = [...this.plantsData]
      this.rootStore.calculationsPageStore.plantsListForAside = [...this._plantsListForAside]
      this.isViewModalAcceptedVault = this.plantsAcceptVault.some((plant) => !plant.success)
      this.plantsAcceptVaultSelected = plantsAcceptVault.filter((el) => !el.success).map((el) => el.plantId)

      return this.isViewModalAcceptedVault
    } catch (e) {
      console.log(e)
    }
  }

  async disAcceptVault() {
    try {
      const plantsDisAcceptVault = await api.calculationsManager.disAcceptVault({
        targetDate: format(this._date, 'yyyy-MM-dd'),
        planingStage: this._planningStage,
        plantIds: this.actionsLeft,
      })
      plantsDisAcceptVault.forEach((acceptedPlant) => {
        const foundPlantIdx = this.plantsData.findIndex(
          (plant) => plant.plantId === acceptedPlant.plantId && acceptedPlant.success,
        )
        if (foundPlantIdx !== -1 && !!this.plantsData[foundPlantIdx]) {
          this.plantsData[foundPlantIdx].accepted = false
        }

        const foundAsidePlantIdx = this._plantsListForAside.findIndex(
          (plant) => plant.plantId === acceptedPlant.plantId && acceptedPlant.success,
        )
        if (foundAsidePlantIdx !== -1 && !!this._plantsListForAside[foundAsidePlantIdx]) {
          this._plantsListForAside[foundAsidePlantIdx].accepted = false
        }
      })
      this.plantsData = [...this.plantsData]
      this.plantsDataOriginal = [...this.plantsData]
      this.rootStore.calculationsPageStore.plantsListForAside = [...this._plantsListForAside]
    } catch (e) {
      console.log(e)
    }
  }

  recalculateInputValues = () => {
    const sidebarPlants = this._plantsListForAside.filter((el) => el.plantId !== 0).filter((el) => el.mixing)

    runInAction(() => {
      this.inputValues = this.plantsData.map((item) => {
        const FLOOD_MODE_WATCH = this.floodsLeft?.some((id) => id === item.plantId) ?? false

        // Если у станции не поменялся режим Половодья, то возвращаем значения из this.inputValues
        const prevInputValues = this.inputValues.find((el) => el.plantId === item.plantId)
        if (prevInputValues && FLOOD_MODE_WATCH === prevInputValues.FLOOD_MODE_WATCH) {
          return prevInputValues
        }

        const E_MAX_E_MIN = item.parameters[PlantParameter.E_MAX_E_MIN]?.value?.value
          ? item.parameters[PlantParameter.E_MAX_E_MIN]?.value?.value / 1000
          : undefined

        let inputValuesPlant = item.inputValues

        const calculate = item.parameters[PlantParameter.E_MAX_E_MIN]?.value?.turnedOn ?? false

        inputValuesPlant = {
          ...inputValuesPlant,
          W_MIN: {
            ...inputValuesPlant['W_MIN'],
            calculate,
          },
        }

        const inputValues: IVaultStore['inputValues'][0] = {
          plantId: item.plantId,
        }

        if (item.inputValues.W_MIN) {
          const SUM_P_MIN_TEMP = this.vaultData.reduce((sum, cur) => {
            const value = cur[`${item.plantId}-P_MIN_RESULT`] ? Number(cur[`${item.plantId}-P_MIN_RESULT`]) : 0

            return sum + value
          }, 0)

          const SUM_P_MIN = SUM_P_MIN_TEMP === 0 ? 0 : round(SUM_P_MIN_TEMP / 1000, 3)

          inputValues['W_MIN'] = FLOOD_MODE_WATCH
            ? SUM_P_MIN
            : item.inputValues.W_MIN.value !== undefined
              ? E_MAX_E_MIN
                ? getValueEminEmax('W_MIN', inputValuesPlant, E_MAX_E_MIN)
                : round(item.inputValues.W_MIN.value / 1000, 3)
              : undefined
        }

        if (item.inputValues.W_MAX) {
          const SUM_P_MAX_TEMP = this.vaultData.reduce((sum, cur) => {
            const value = cur[`${item.plantId}-P_MIN_RESULT`] ? Number(cur[`${item.plantId}-P_MAX_RESULT`]) : 0

            return sum + value
          }, 0)

          const SUM_P_MAX = SUM_P_MAX_TEMP === 0 ? 0 : round(SUM_P_MAX_TEMP / 1000, 3)

          inputValues['W_MAX'] = FLOOD_MODE_WATCH
            ? SUM_P_MAX
            : item.inputValues.W_MAX.value !== undefined
              ? round(item.inputValues.W_MAX.value / 1000, 3)
              : undefined
        }

        if (item.inputValues.P_GEN_TARGET) {
          const SUM_P_MAX_TEMP = this.vaultData.reduce((sum, cur) => {
            const value = cur[`${item.plantId}-P_MIN_RESULT`] ? Number(cur[`${item.plantId}-P_MAX_RESULT`]) : 0

            return sum + value
          }, 0)

          const SUM_P_MAX = SUM_P_MAX_TEMP === 0 ? 0 : round(SUM_P_MAX_TEMP / 1000, 3)

          inputValues['P_GEN_TARGET'] = FLOOD_MODE_WATCH
            ? SUM_P_MAX
            : item.inputValues.P_GEN_TARGET.value !== undefined
              ? round(item.inputValues.P_GEN_TARGET.value / 1000, 3)
              : undefined
        }

        if (item.inputValues.FLOOD_MODE_WATCH) {
          inputValues['FLOOD_MODE_WATCH'] = FLOOD_MODE_WATCH
        }

        return inputValues
      })
      this.inputValues = sidebarPlants.map((plant) => {
        const index = this.inputValues.findIndex((value) => value.plantId === plant.plantId)

        if (index === -1) {
          return {
            plantId: plant.plantId,
          }
        }

        return this.inputValues[index]
      })
      this._updateInputResultProps()
    })
  }

  changeInputValue = (value: string, key: InputValueKeys, coll: number) => {
    const findIndex = this.inputResultCellIndexes.findIndex((el) => el === coll)
    if (this.vaultLoadDataStatus === 'DONE' && findIndex !== -1) {
      const plantIdx = Math.trunc(findIndex / 3)
      const plants = this._plantsListForAside.map((plant) => {
        const plantType = this.plantsData.find((el) => el.plantId === plant.plantId)?.plantType ?? null

        return { ...plant, plantType }
      })
      const plantId = plants.filter((el) => el.mixing)[plantIdx]?.plantId ?? undefined
      if (plantId) {
        const index = this.inputValues.findIndex((value) => value.plantId === plantId)
        const plantFullData = this.plantsData.find((el) => el.plantId === plantId)
        if (index === -1) return
        runInAction(() => {
          if (key === 'W_MAX' && plantFullData?.parameters?.E_MAX_E_MIN) {
            this.inputValues = [
              // Сохраняем значения массива без изменений с 0 по index
              ...this.inputValues.slice(0, index),
              // Вставка обновленных значений W_MIN и W_MAX
              // Также логика вычисления W_MIN и W_MAX в зависимости от E_MAX_E_MIN
              {
                ...this.inputValues[index],
                W_MIN: plantFullData?.parameters?.E_MAX_E_MIN?.value.turnedOn
                  ? getValueEminEmax(
                      'W_MIN',
                      {
                        W_MIN: {
                          value: !Number.isNaN(parseFloat(String(this.inputValues[index].W_MIN)))
                            ? Number(this.inputValues[index].W_MIN) * 1000
                            : undefined,
                          calculate: plantFullData?.parameters?.E_MAX_E_MIN?.value.turnedOn,
                        },
                        W_MAX: {
                          value: value ? Number(value) * 1000 : '',
                        },
                      },
                      plantFullData?.parameters?.E_MAX_E_MIN?.value.value / 1000,
                    )
                  : this.inputValues[index]?.W_MIN,
                W_MAX: value
                  ? getValueEminEmax(
                      'W_MAX',
                      {
                        W_MAX: {
                          value: Number(value) * 1000,
                          calculate: plantFullData?.parameters?.E_MAX_E_MIN?.value.turnedOn,
                        },
                      },
                      plantFullData?.parameters?.E_MAX_E_MIN?.value.value / 1000,
                    )
                  : value,
              },
              // Сохраняем значения массива без изменений с index по последний
              ...this.inputValues.slice(index + 1),
            ]
          } else {
            this.inputValues = [
              // Сохраняем значения массива без изменений с 0 по index
              ...this.inputValues.slice(0, index),
              // Вставка обновленных значений
              {
                ...this.inputValues[index],
                [key]: value ? round(Number(value), 3) : value,
              },
              // Сохраняем значения массива без изменений с index по последний
              ...this.inputValues.slice(index + 1),
            ]
          }
          this.inputValuesChanged = true
          this._updateInputResultProps()
          this.validate(this.vaultSpreadsheet.data, null)
        })
      } else {
        throw new Error('plantId не определен !')
      }
    }
  }

  handleAfterChange = (changes: Handsontable.CellChange[] | null) => {
    if (changes) {
      let avrchms: number[] = []
      let rguAvrchms: { [key: number]: number[] } = {}
      let avrchms_plant_ids: number[] = []
      let cursor: number = 0
      const newEditCellUp = [...this.editCellsUp]
      this._plantsListForAside
        .filter((el) => el.mixing)
        .forEach((el) => {
          const plant = this.plantsData.find((ps) => ps.plantId === el.plantId)
          if (!plant) return
          const rgus = plant.rgus
          const stationAvrchmIdx = cursor + (1 + (rgus.length || 0)) * 3 + 1
          avrchms = [...avrchms, stationAvrchmIdx]
          rguAvrchms = {
            ...rguAvrchms,
            [stationAvrchmIdx]: (rgus || []).map((_, idx) => stationAvrchmIdx + 2 * (idx + 1)),
          }
          avrchms_plant_ids = [...avrchms_plant_ids, plant.plantId]
          cursor = cursor + 11 * (1 + (rgus.length || 0))
        })
      let newData = toJS(this.vaultSpreadsheet.data)
      const cellUpdates = new Map<number, Partial<IVaultStore['vaultSpreadsheet']['cell'][0]>>()

      changes.forEach(([row, col, _, newValue]) => {
        const { indexStationAVRCHM } = this._getPlantParamsByAvrchmColIdx(rguAvrchms, col as number)
        const plantId = indexStationAVRCHM !== -1 ? avrchms_plant_ids[indexStationAVRCHM] : -1
        newEditCellUp.push(`${row}-${col}`)
        if (indexStationAVRCHM !== -1) {
          this.plantsData = this.plantsData.map((plant) => {
            if (plant.plantId === avrchms_plant_ids[indexStationAVRCHM]) {
              const allowedZones = plant.allowedZones.map((zone, index) => {
                if (index === row) {
                  return { ...zone, zones: [] }
                }

                return zone
              })
              const rgus = plant.rgus.map((rgu) => {
                const allowedZones = rgu.allowedZones.map((zone, index) => {
                  if (index === row) {
                    return { ...zone, zones: [] }
                  }

                  return zone
                })

                return { ...rgu, allowedZones }
              })

              return { ...plant, allowedZones, rgus }
            }

            return plant
          })
          const rgus = this.plantsData.find((ps) => ps.plantId === plantId)?.rgus ?? []
          const key_P_MAX = avrchms[indexStationAVRCHM] - 2 - rgus.length * 3
          const key_P_MIN = avrchms[indexStationAVRCHM] - 4 - rgus.length * 3
          const rgu_P_MAX_arr = new Array(rgus.length).fill(null).map((_, idx) => key_P_MAX + (idx + 1) * 3)
          const rgu_P_MIN_arr = new Array(rgus.length).fill(null).map((_, idx) => key_P_MIN + (idx + 1) * 3)
          // value из массива change не передается, потому что в spreadsheet.data автоматически обновляются данные со стороны библиотеки Handsontable
          newData = newData.map((item, index) => {
            if (index === row) {
              return item.map((el, indexx) => {
                if (indexx === key_P_MAX) {
                  return destroyPMaxFormula(String(el))
                }
                if (indexx === key_P_MIN) {
                  return destroyPMinFormula(String(el))
                }
                if (rgu_P_MAX_arr.includes(indexx)) {
                  return destroyPMaxFormula(String(el))
                }
                if (rgu_P_MIN_arr.includes(indexx)) {
                  return destroyPMinFormula(String(el))
                }

                return el
              })
            }

            return item
          })
        }

        const notEmptyValue = newValue !== '' && newValue !== null
        this.vaultSpreadsheet.cell.forEach((item, index) => {
          const manual = notEmptyValue
          const fixed = notEmptyValue ? item?.fixed : notEmptyValue
          if (item.row === row && item.col === col && item.idStation === plantId) {
            cellUpdates.set(index, { manual, fixed, allowedZones: [] })
          } else if (item.row === row && item.col === col) {
            cellUpdates.set(index, { manual, fixed })
          } else if (item.row === row && item.idStation === plantId) {
            cellUpdates.set(index, { allowedZones: [] })
          }
        })
      })

      runInAction(() => {
        cellUpdates.forEach((updates, index) => {
          Object.assign(this.vaultSpreadsheet.cell[index], updates)
        })
        if (newData !== null) {
          this.validate(newData, changes)
        }
        this.editCellsUp = Array.from(new Set(newEditCellUp))
        this._updateInputResultProps()
      })

      // Синхронизация АВРЧМ со сводом происходит после применения изменений на Своде,
      // потому что в таблице АВРЧМ применяется валидация, которая есть на Своде
      const changesForSync: [Handsontable.CellChange, number][] = []
      changes.forEach(([, prop], idx) => {
        const { indexStationAVRCHM, isRguAvrchm } = this._getPlantParamsByAvrchmColIdx(rguAvrchms, prop as number)
        const plantId = indexStationAVRCHM !== -1 ? avrchms_plant_ids[indexStationAVRCHM] : -1
        if (!isRguAvrchm) {
          // добавляем для валидации нового значения
          changesForSync.push([changes[idx], plantId])
        } else {
          // изменилось РГЕ, сохраняем для перевалидации
          const [row, col, prevValue] = changes[idx]
          changesForSync.push([[row, col, prevValue, undefined], plantId])
        }
      })

      this._avrchmStore.syncCellChangeWithVault(changesForSync)
    }
  }

  selectSpreadsheetCoords = () => {
    this.selectedCellsBeforeFix = getSpreadsheetSelectedCells(this.handsontableInstance)
  }

  // При нажатии вне области таблицы необходимо сбрасывать состояние по выбранным ячейка для установки/снятия фиксации
  // при этом, если нажата кнопка установить/снять фиксацию, то выполнится соответствующй обработчик и значения не должны быть сброшены
  handleClickAway: ClickAwayListenerProps['onClickAway'] = (event) => {
    const fixingElementId = (event.target as HTMLElement).getAttribute('id')
    if (fixingElementId !== 'closeLock' && fixingElementId !== 'openLock') {
      runInAction(() => {
        this.selectedCellsBeforeFix = []
        this._avrchmStore.selectedCellsBeforeFix = []
      })
    }
  }
}
